[system]
# Load language from environment variable(It is set by the hook)
language = "${env:DBGPT_LANG:-zh}"
api_keys = []
encrypt_key = "your_secret_key"

# Server Configurations
[service.web]
host = "0.0.0.0"
port = 5670

[service.web.database]
type = "sqlite"
path = "pilot/meta_data/dbgpt.db"
[service.model.worker]
host = "127.0.0.1"

[rag.storage]
[rag.storage.vector]
type = "chroma"
persist_path = "pilot/data"

# Model Configurations
[models]
[[models.llms]]
name = "deepseek-v3"
provider = "proxy/infiniai"
api_key = "${env:INFINIAI_API_KEY}"

[[models.embeddings]]
name = "bge-m3"
provider = "proxy/openai"
api_url = "https://cloud.infini-ai.com/maas/v1/embeddings"
api_key = "${env:INFINIAI_API_KEY}"

[[models.rerankers]]
name = "bge-reranker-v2-m3"
provider = "proxy/infiniai"
api_key = "${env:INFINIAI_API_KEY}" 