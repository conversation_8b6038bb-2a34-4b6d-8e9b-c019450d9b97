{"name": "dbgpt-docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "3.4.0", "@docusaurus/preset-classic": "3.4.0", "@docusaurus/remark-plugin-npm2yarn": "^3.4.0", "@docusaurus/theme-mermaid": "^3.4.0", "@easyops-cn/docusaurus-search-local": "^0.38.1", "@mdx-js/react": "^3.0.0", "clsx": "^1.2.1", "flickity": "^2.2.1", "prism-react-renderer": "^2.1.0", "process": "^0.11.10", "react": "^18.0.0", "react-dom": "^18.0.0", "react-flickity-component": "^4.0.6", "winston": "^3.13.1"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.4.0", "@docusaurus/types": "3.4.0"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0"}}