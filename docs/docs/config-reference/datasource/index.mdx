---
title: "datasource"
description: "datasource Configuration"
---

# datasource Configuration

This document provides an overview of all configuration classes in datasource type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "ClickhouseParameters",
    "description": "Columnar database for high-performance analytics and real-time queries.",
    "link": "./conn_clickhouse_clickhouseparameters_4a1237"
  },
  {
    "name": "DorisParameters",
    "description": "A new-generation open-source real-time data warehouse.",
    "link": "./conn_doris_dorisparameters_e33c53"
  },
  {
    "name": "DuckDbConnectorParameters",
    "description": "In-memory analytical database with efficient query processing.",
    "link": "./conn_duckdb_duckdbconnectorparameters_c672c7"
  },
  {
    "name": "HiveParameters",
    "description": "A distributed fault-tolerant data warehouse system.",
    "link": "./conn_hive_hiveparameters_ec3601"
  },
  {
    "name": "MSSQLParameters",
    "description": "Powerful, scalable, secure relational database system by Microsoft.",
    "link": "./conn_mssql_mssqlparameters_d79d1c"
  },
  {
    "name": "MySQLParameters",
    "description": "Fast, reliable, scalable open-source relational database management system.",
    "link": "./conn_mysql_mysqlparameters_4393c4"
  },
  {
    "name": "OceanBaseParameters",
    "description": "An Ultra-Fast & Cost-Effective Distributed SQL Database.",
    "link": "./conn_oceanbase_oceanbaseparameters_260d2d"
  },
  {
    "name": "PostgreSQLParameters",
    "description": "Powerful open-source relational database with extensibility and SQL standards.",
    "link": "./conn_postgresql_postgresqlparameters_22efa5"
  },
  {
    "name": "RDBMSDatasourceParameters",
    "description": "RDBMS datasource parameters.",
    "link": "./base_rdbmsdatasourceparameters_4f774f"
  },
  {
    "name": "SQLiteConnectorParameters",
    "description": "Lightweight embedded relational database with simplicity and portability.",
    "link": "./conn_sqlite_sqliteconnectorparameters_82c8b5"
  },
  {
    "name": "SparkParameters",
    "description": "Unified engine for large-scale data analytics.",
    "link": "./conn_spark_sparkparameters_174bbc"
  },
  {
    "name": "StarRocksParameters",
    "description": "An Open-Source, High-Performance Analytical Database.",
    "link": "./conn_starrocks_starrocksparameters_e511f7"
  },
  {
    "name": "TuGraphParameters",
    "description": "TuGraph is a high-performance graph database jointly developed by Ant Group and Tsinghua University.",
    "link": "./conn_tugraph_tugraphparameters_0c844e"
  },
  {
    "name": "VerticaParameters",
    "description": "Vertica is a strongly consistent, ACID-compliant, SQL data warehouse, built for the scale and complexity of today`s data-driven world.",
    "link": "./conn_vertica_verticaparameters_c712b8"
  },
]} />

