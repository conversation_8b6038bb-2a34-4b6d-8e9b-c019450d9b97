---
title: "OpenAPIRerankerDeployModelParameters Configuration"
description: "OpenAPI Reranker Deploy Model Parameters."
---

import { ConfigDetail } from "@site/src/components/mdx/ConfigDetail";

<ConfigDetail config={{
  "name": "OpenAPIRerankerDeployModelParameters",
  "description": "OpenAPI Reranker Deploy Model Parameters.",
  "documentationUrl": "",
  "parameters": [
    {
      "name": "name",
      "type": "string",
      "required": true,
      "description": "The name of the model."
    },
    {
      "name": "provider",
      "type": "string",
      "required": false,
      "description": "The provider of the model. If model is deployed in local, this is the inference type. If model is deployed in third-party service, this is platform name('proxy/<platform>')",
      "defaultValue": "proxy/openapi"
    },
    {
      "name": "verbose",
      "type": "boolean",
      "required": false,
      "description": "Show verbose output.",
      "defaultValue": "False"
    },
    {
      "name": "concurrency",
      "type": "integer",
      "required": false,
      "description": "Model concurrency limit",
      "defaultValue": "50"
    },
    {
      "name": "api_url",
      "type": "string",
      "required": false,
      "description": "The URL of the rerank API.",
      "defaultValue": "http://localhost:8100/v1/beta/relevance"
    },
    {
      "name": "api_key",
      "type": "string",
      "required": false,
      "description": "The API key for the rerank API."
    },
    {
      "name": "backend",
      "type": "string",
      "required": false,
      "description": "The real model name to pass to the provider, default is None. If backend is None, use name as the real model name."
    },
    {
      "name": "timeout",
      "type": "integer",
      "required": false,
      "description": "The timeout for the request in seconds.",
      "defaultValue": "60"
    }
  ]
}} />

