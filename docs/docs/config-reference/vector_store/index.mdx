---
title: "vector_store"
description: "vector_store Configuration"
---

# vector_store Configuration

This document provides an overview of all configuration classes in vector_store type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "ChromaVectorConfig",
    "description": "Chroma vector store config.",
    "link": "./chroma_store_chromavectorconfig_16224f"
  },
  {
    "name": "ElasticsearchStoreConfig",
    "description": "Elasticsearch vector config.",
    "link": "./elastic_store_elasticsearchstoreconfig_15bdb6"
  },
  {
    "name": "MilvusVectorConfig",
    "description": "Milvus vector config.",
    "link": "./milvus_store_milvusvectorconfig_20af52"
  },
  {
    "name": "OceanBaseConfig",
    "description": "OceanBase vector store config.",
    "link": "./oceanbase_store_oceanbaseconfig_220e36"
  },
  {
    "name": "PGVectorConfig",
    "description": "PG vector config.",
    "link": "./pgvector_store_pgvectorconfig_3ef448"
  },
  {
    "name": "WeaviateVectorConfig",
    "description": "Weaviate vector config.",
    "link": "./weaviate_store_weaviatevectorconfig_093ce3"
  },
]} />

