---
title: "TuGraphStoreConfig Configuration"
description: "TuGraph store config."
---

import { ConfigDetail } from "@site/src/components/mdx/ConfigDetail";

<ConfigDetail config={{
  "name": "TuGraphStoreConfig",
  "description": "TuGraph store config.",
  "documentationUrl": "",
  "parameters": [
    {
      "name": "host",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "127.0.0.1"
    },
    {
      "name": "port",
      "type": "integer",
      "required": false,
      "description": "",
      "defaultValue": "7687"
    },
    {
      "name": "username",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "admin"
    },
    {
      "name": "password",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "73@TuGraph"
    },
    {
      "name": "vertex_type",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "entity"
    },
    {
      "name": "document_type",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "document"
    },
    {
      "name": "chunk_type",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "chunk"
    },
    {
      "name": "edge_type",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "relation"
    },
    {
      "name": "include_type",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "include"
    },
    {
      "name": "next_type",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "next"
    },
    {
      "name": "plugin_names",
      "type": "string",
      "required": false,
      "description": "",
      "defaultValue": "['leiden']"
    },
    {
      "name": "enable_summary",
      "type": "boolean",
      "required": false,
      "description": "",
      "defaultValue": "True"
    },
    {
      "name": "enable_similarity_search",
      "type": "boolean",
      "required": false,
      "description": "",
      "defaultValue": "False"
    }
  ]
}} />

