---
title: "service"
description: "service Configuration"
---

# service Configuration

This document provides an overview of all configuration classes in service type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "DBModelRegistryParameters",
    "description": "Database model registry parameters.",
    "link": "./parameter_dbmodelregistryparameters_87d036"
  },
]} />

