---
title: "Model Serve Configurations Configuration"
description: "This configuration is for the model serve module."
---

import { ConfigDetail } from "@site/src/components/mdx/ConfigDetail";

<ConfigDetail config={{
  "name": "ServeConfig",
  "description": "This configuration is for the model serve module.",
  "documentationUrl": null,
  "parameters": [
    {
      "name": "api_keys",
      "type": "string",
      "required": false,
      "description": "API keys for the endpoint, if None, allow all"
    },
    {
      "name": "model_storage",
      "type": "string",
      "required": false,
      "description": "The storage type of model configures, if None, use the default storage(current database). When you run in light mode, it will not use any storage.",
      "validValues": [
        "database",
        "memory"
      ]
    }
  ]
}} />

