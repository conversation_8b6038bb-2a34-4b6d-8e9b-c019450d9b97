---
title: "serve"
description: "serve Configuration"
---

# serve Configuration

This document provides an overview of all configuration classes in serve type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "ServeConfig",
    "description": "This configuration is for the datasource serve module.",
    "link": "./config_serveconfig_63f1e9"
  },
  {
    "name": "ServeConfig",
    "description": "Parameters for the serve command",
    "link": "./config_serveconfig_adbd6f"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the conversation serve module.",
    "link": "./config_serveconfig_313252"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the hub dbgpts serve module.",
    "link": "./config_serveconfig_ec2d70"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the my dbgpts serve module.",
    "link": "./config_serveconfig_1a9284"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the evaluate serve module.",
    "link": "./config_serveconfig_8839e0"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the feedback serve module.",
    "link": "./config_serveconfig_fa1f35"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the file serve module. In DB-GPT, you can store yourfiles in the file server.",
    "link": "./config_serveconfig_cb64c6"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the flow serve module.",
    "link": "./config_serveconfig_c0b589"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the libro serve module.",
    "link": "./config_serveconfig_b1c2b9"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the model serve module.",
    "link": "./config_serveconfig_7a0577"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the prompt serve module.",
    "link": "./config_serveconfig_854dad"
  },
  {
    "name": "ServeConfig",
    "description": "This configuration is for the RAG serve module.",
    "link": "./config_serveconfig_7889f9"
  },
]} />

