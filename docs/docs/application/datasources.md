# Datasources

The DB-GPT data source module is designed to manage the structured and semi-structured data assets of an enterprise, connect databases, data warehouses, data lakes, etc. to the DB-GPT framework, and quickly build data-based intelligent applications and large models. Currently, DB-GPT supports some common data sources and also supports custom extensions.

<p align="center">
  <img src={'/img/app/datasource.jpg'} width="800px" />
</p>


You can add data sources through the upper right corner **Add a data source** button to add. In the pop-up dialog box, select the corresponding database type and fill in the required parameters to complete the addition.

<p align="center">
  <img src={'/img/app/datasource_add.jpg'} width="800px" />
</p>
