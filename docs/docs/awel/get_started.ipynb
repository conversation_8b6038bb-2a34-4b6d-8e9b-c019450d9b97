{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_position: 0\n", "title: Get started\n", "keywords: [awel.dag]\n", "---"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["AWEL(Agentic Workflow Expression Language) makes it easy to build complex llm apps, and it provides great functionality and flexibility. "]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Basic example use AWEL: http request + output rewrite\n", "\n", "The basic usage about AWEL is to build a http request and rewrite some output value. To see how this works, let's see an example.\n", "\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### DAG Planning\n", "First, let's look at an introductory example of basic AWEL orchestration. The core function of the example is the handling of input and output for an HTTP request. Thus, the entire orchestration consists of only two steps:\n", "- HTTP Request\n", "- Processing HTTP Response Result\n", "\n", "In DB-GPT, some basic dependent operators have already been encapsulated and can be referenced directly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["from dbgpt._private.pydantic import BaseModel, Field\n", "from dbgpt.core.awel import DAG, HttpTrigger, MapOperator"], "outputs": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Custom Operator\n", "\n", "Define an HTTP request body that accepts two parameters: name and age.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class TriggerReqBody(BaseModel):\n", "    name: str = Field(..., description=\"User name\")\n", "    age: int = Field(18, description=\"User age\")"], "outputs": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Define a Request handler operator called `RequestHandleOperator`, which is an operator that extends the basic `MapOperator`. The actions of the `RequestHandleOperator` are straightforward: parse the request body and extract the name and age fields, then concatenate them into a sentence. For example:\n", "> \"Hello, <PERSON><PERSON><PERSON>, your age is 18.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class RequestHandleOperator(MapOperator[TriggerReqBody, str]):\n", "    def __init__(self, **kwargs):\n", "        super().__init__(**kwargs)\n", "\n", "    async def map(self, input_value: TriggerReqBody) -> str:\n", "        print(f\"Receive input value: {input_value}\")\n", "        return f\"Hello, {input_value.name}, your age is {input_value.age}\""], "outputs": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### DAG Pipeline\n", "\n", "After writing the above operators, they can be assembled into a DAG orchestration. This DAG has a total of two nodes: the first node is an `HttpTrigger`, which primarily processes HTTP requests (this operator is built into DB-GPT), and the second node is the newly defined `RequestHandleOperator` that processes the request body. The DAG code below can be used to link the two nodes together.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["with DAG(\"simple_dag_example\") as dag:\n", "    trigger = HttpTrigger(\"/examples/hello\", request_body=TriggerReqBody)\n", "    map_node = RequestHandleOperator()\n", "    trigger >> map_node"], "outputs": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Access Verification\n", "\n", "Before performing access verification, the project needs to be started first: `python dbgpt/app/dbgpt_server.py`\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "powershell"}}, "source": ["\n", "% curl -X GET http://127.0.0.1:5000/api/v1/awel/trigger/examples/hello\\?name\\=zhangsan\n", "\"Hello, <PERSON><PERSON><PERSON>, your age is 18\""], "outputs": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Of course, to make it more convenient for users to test, we also provide a test environment. This test environment allows testing without starting the dbgpt_server. Add the following code below simple_dag_example, then directly run the simple_dag_example.py script to run the test script without starting the project."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "powershell"}}, "source": ["if __name__ == \"__main__\":\n", "    if dag.leaf_nodes[0].dev_mode:\n", "        # Development mode, you can run the dag locally for debugging.\n", "        from dbgpt.core.awel import setup_dev_environment\n", "\n", "        setup_dev_environment([dag], port=5555)\n", "    else:\n", "        # Production mode, DB-GPT will automatically load and execute the current file after startup.\n", "        pass"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "powershell"}}, "source": ["curl -X GET http://127.0.0.1:5555/api/v1/awel/trigger/examples/hello\\?name\\=zhangsan\n", "\"Hello, <PERSON><PERSON><PERSON>, your age is 18\""], "outputs": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["[simple_dag_example](/examples/awel/simple_dag_example.py)"]}], "metadata": {"kernelspec": {"display_name": "dbgpt_env", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.13 (main, Sep 11 2023, 08:16:02) [Clang 14.0.6 ]"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "f8b6b0e04f284afd2fbb5e4163e7d03bbdc845eaeb6e8c78fae04fce6b51dae6"}}}, "nbformat": 4, "nbformat_minor": 2}