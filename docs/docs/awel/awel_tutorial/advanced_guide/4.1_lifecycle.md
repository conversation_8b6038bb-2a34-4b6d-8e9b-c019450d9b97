# 4.1 AWEL Lifecycle

## Task Lifecycle Hooks

Task lifecycle hooks are a set of methods that can be implemented in a task to perform
actions at different stages of the task lifecycle. The following hooks are available:

- `before_dag_run`: Execute before DAG run
- `after_dag_end`: Execute after DAG end

### Example

Create a new file `lifecycle_hooks.py` in the `awel_tutorial` directory and add the following code:

```python
import asyncio
from dbgpt.core.awel import DAG, MapOperator

class MyLifecycleTask(MapOperator[str, str]):
    async def before_dag_run(self):
        print("Before DAG run")

    async def after_dag_end(self):
        print("After DAG end")

    async def map(self, x: str) -> str:
        return f"Hello, {x}!"

with DAG("awel_lifecycle_hooks") as dag:
    task = MyLifecycleTask()

print(asyncio.run(task.call("world")))
```

And run the following command to execute the code:

```bash
poetry run python awel_tutorial/lifecycle_hooks.py
```

And the main output should look like this:

```plaintext
Before DAG run
After DAG end
Hello, world!
```