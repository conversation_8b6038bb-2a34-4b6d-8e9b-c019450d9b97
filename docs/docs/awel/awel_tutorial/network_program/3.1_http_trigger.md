# 3.1 Http Trigger

## Introduction

In this chapter, we will start to focus on how to use AWEL to develop a network program. 
First, we will create a simple HTTP trigger that receives a request and returns a response.

`HttpTrigger` is a special `InputOperator`.

## Installation

We have already created a project named `awel-tutorial` in the 
[previous chapter](/docs/awel/awel_tutorial/getting_started/1.1_hello_world#creating-a-project)
and added the `dbgpt` dependency. 

To use the `HttpTrigger` operator, we need to install the `fastapi` and `uvicorn` packages.

```bash
poetry add fastapi uvicorn
```

The output should look like this:

```plaintext
➜  awel-tutorial poetry add fastapi uvicorn
Using version ^0.110.0 for fastapi
Using version ^0.27.1 for uvicorn

Updating dependencies
Resolving dependencies... (2.7s)

Package operations: 7 installs, 0 updates, 0 removals

  • Installing sniffio (1.3.1)
  • Installing anyio (4.3.0)
  • Installing click (8.1.7)
  • Installing h11 (0.14.0)
  • Installing starlette (0.36.3)
  • Installing fastapi (0.110.0)
  • Installing uvicorn (0.27.1)

Writing lock file
```

## First HTTP Trigger

Create a new file named `frist_http_trigger_hello.py` in the `awel_tutorial` directory and add the following code:

```python
from dbgpt.core.awel import DAG, HttpTrigger, MapOperator, setup_dev_environment

with DAG("awel_hello_world") as dag:
    trigger_task = HttpTrigger(endpoint="/awel_tutorial/hello_world")
    task = MapOperator(map_function=lambda x: f"Hello, world!")
    trigger_task >> task  
    
setup_dev_environment([dag], port=5555)
```

And run the following command to execute the code:

```bash
poetry run python awel_tutorial/frist_http_trigger_hello.py
```

And the main output should look like this:

```plaintext
2024-03-03 16:26:57 | INFO | dbgpt.core.awel.trigger.http_trigger | Mount http trigger success, path: /api/v1/awel/trigger/awel_tutorial/hello_world
2024-03-03 16:26:57 | INFO | dbgpt.core.awel.trigger.trigger_manager | Include router <fastapi.routing.APIRouter object at 0x10ed64e50> to prefix path /api/v1/awel/trigger
INFO:     Started server process [69774]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:5555 (Press CTRL+C to quit)
```

In AWEL, all HTTP endpoints are prefixed with `/api/v1/awel/trigger` by default.

Now, open a new terminal and run the following command to send a request to the server:

```bash
curl -X GET http://127.0.0.1:5555/api/v1/awel/trigger/awel_tutorial/hello_world
```

The output should look like this:

```plaintext
"Hello, world!"
```

Congratulations! You have created your first HTTP trigger.

Then you can stop the server by pressing `Ctrl+C` in the terminal.
 
## How It Works

In above code, we created a `HttpTrigger` operator and a `MapOperator` operator.
`HttpTrigger` defines the endpoint of the HTTP request, and the method of the request 
is "GET" by default.

The `setup_dev_environment` function is used to start the server and register dags, it 
will block the main thread if there are `HttpTrigger` operators in the DAG and listen 
on 5555 port by default.

When the server receives a request, it will call the `MapOperator` operator to process 
the request and return the result.

In `HttpTrigger`, you can configure the endpoint, method, request body, response body, 
response status code, etc.

In next section, we will introduce more about the `HttpTrigger`.
