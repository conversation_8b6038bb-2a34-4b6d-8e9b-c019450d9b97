# Action Introduction

> The action module is responsible for translating the agent’s decisions into specific outcomes. 
> This module is located at the most downstream position and directly interacts with the environment. 
> It is influenced by the profile, memory, and planning modules.

## Actions Overview

In DB-GPT, any agent must have an action.


There are four perspectives according the paper 
[A survey on large language model based autonomous agents](https://link.springer.com/article/10.1007/s11704-024-40231-1):

### Action Goals

What the agent wants to achieve with the action?

1. Task Completion: Complete specific tasks, write a function in software development,
and make an iron pick in the game.

2. Communication: Communicate with other agents.

3. Environment exploration: Explore unfamiliar environments to expand its perception
and strike a balance between exploring and exploiting.

### Action Production

How are the actions generated?
1. Action via memory recollection. In this strategy, the action is generated by 
extracting information from the agent memory according to the current task. The task 
and the extracted memories are used as prompts to trigger the agent actions.

2. Action via plan following. In this strategy, the agent takes actions following its pregenerated plans.

### Action Space

What are the available actions?
Action space refers to the set of possible actions that can be performed by the agent.
In general, we can roughly divide these actions into two classes: 
(1) external tools and(2) internal knowledge of the LLMs.

### Action Impact

What are the consequences of the actions?

Action impact refers to the consequences of the action. In fact, the action impact can encompass numerous instances.

Here are some examples of action impact:
1. Changing environments: Agents can directly alter environment states by actions, such 
as moving their positions, collecting items, and constructing buildings.
2. Altering internal states: Actions taken by the agent can also change the agent itself, 
including updating memories, forming new plans, acquiring novel knowledge, and more.
3. Triggering new actions: In the task completion process, one agent action can be triggered by another one.

## Actions In DB-GPT Agents

In previous [Write Your Custom Agent](../../introduction/custom_agents#create-a-custom-action), 
you have seen a basic example of an action in the agent. It is a simple way to define the agent's action.


In previous [Tool Use](../../introduction/tools.md), you have seen how to write tools 
to help LLMs to complete the tasks. And we know than `Tool` is a kind of `Resource`.
In following sections, we will show you how to use `Resource` in the action module.
