
# DB-GPT: 用私有化LLM技术定义数据库下一代交互方式
## DB-GPT 是什么？
🤖️ **DB-GPT是一个开源的AI原生数据应用开发框架(AI Native Data App Development framework with AWEL(Agentic Workflow Expression Language) and Agents)。**
目的是构建大模型领域的基础设施，通过开发多模型管理(SMMF)、Text2SQL效果优化、RAG框架以及优化、Multi-Agents框架协作、AWEL(智能体工作流编排)等多种技术能力，让围绕数据库构建大模型应用更简单，更方便。 
🚀 **数据3.0 时代，基于模型、数据库，企业/开发者可以用更少的代码搭建自己的专属应用。**
## 效果演示
### AI原生数据智能应用

---

- 🔥🔥🔥 [V0.5.0发布——通过工作流与智能体开发原生数据应用](https://www.yuque.com/eosphoros/dbgpt-docs/owcrh9423f9rqkg2)

---

### Data Agents
![](https://github.com/eosphoros-ai/DB-GPT/assets/17919400/37d116fc-d9dd-4efa-b4df-9ab02b22541c#id=KpPbI&originHeight=1880&originWidth=3010&originalType=binary&ratio=1&rotation=0&showTitle=false&status=done&style=none)
![](https://github.com/eosphoros-ai/DB-GPT/assets/17919400/a7bf6d65-92d1-4f0e-aaf0-259ccdde22fd#id=EHUr0&originHeight=1872&originWidth=3396&originalType=binary&ratio=1&rotation=0&showTitle=false&status=done&style=none)
![](https://github.com/eosphoros-ai/DB-GPT/assets/17919400/1849a79a-f7fd-40cf-bc9c-b117a041dd6a#id=gveW4&originHeight=1868&originWidth=2996&originalType=binary&ratio=1&rotation=0&showTitle=false&status=done&style=none)
## 目录

- [架构方案](#架构方案)
- [安装](#安装)
- [特性简介](#特性一览)
- [贡献](#贡献)
- [路线图](#路线图)
- [联系我们](#联系我们)
## 架构方案
![image.png](https://intranetproxy.alipay.com/skylark/lark/0/2024/png/26456775/1724764757479-314c8ed2-24e6-4cc2-8a29-e84e626d6755.png#clientId=u47bade0c-6d5b-4&from=paste&height=721&id=u6344fee6&originHeight=1442&originWidth=1590&originalType=binary&ratio=2&rotation=0&showTitle=false&size=766959&status=done&style=none&taskId=u0f69fc62-9392-468b-a990-84de8e3a3eb&title=&width=795)
核心能力主要有以下几个部分:

- **RAG(Retrieval Augmented Generation)**，RAG是当下落地实践最多，也是最迫切的领域，DB-GPT目前已经实现了一套基于RAG的框架，用户可以基于DB-GPT的RAG能力构建知识类应用。 
- **GBI**：生成式BI是DB-GPT项目的核心能力之一，为构建企业报表分析、业务洞察提供基础的数智化技术保障。 
- **微调框架**:  模型微调是任何一个企业在垂直、细分领域落地不可或缺的能力，DB-GPT提供了完整的微调框架，实现与DB-GPT项目的无缝打通，在最近的微调中，基于spider的准确率已经做到了82.5%
- **数据驱动的Multi-Agents框架**:  DB-GPT提供了数据驱动的自进化Multi-Agents框架，目标是可以持续基于数据做决策与执行。 
- **数据工厂**: 数据工厂主要是在大模型时代，做可信知识、数据的清洗加工。 
- **数据源**: 对接各类数据源，实现生产业务数据无缝对接到DB-GPT核心能力。
### 智能体编排语言(AWEL)
AWEL（Agentic Workflow Expression Language）是一套专为大模型应用开发设计的智能体工作流表达语言，它提供了强大的功能和灵活性。通过 AWEL API 您可以专注于大模型应用业务逻辑的开发，而不需要关注繁琐的模型和环境细节，AWEL 采用分层 API 的设计， AWEL 的分层 API 设计架构如下图所示：

![image.png](https://cdn.nlark.com/yuque/0/2023/png/23108892/1700743735979-fcae1255-5b21-4071-a805-84d9f98247ef.png#averageHue=%23efefef&clientId=u62c750d6-91b4-4&from=paste&height=588&id=ua7e2a75b&originHeight=819&originWidth=586&originalType=binary&ratio=2&rotation=0&showTitle=false&size=101075&status=done&style=shadow&taskId=u753583cb-7d4f-4267-962d-a892e5150d2&title=&width=421)

AWEL在设计上分为三个层次，依次为算子层、AgentFrame层以及DSL层，以下对三个层次做简要介绍。 

- 算子层

算子层是指LLM应用开发过程中一个个最基本的操作原子，比如在一个RAG应用开发时。 检索、向量化、模型交互、Prompt处理等都是一个个基础算子。 在后续的发展中，框架会进一步对算子进行抽象与标准化设计。 可以根据标准API快速实现一组算子。

- AgentFrame层

AgentFrame层将算子做进一步封装，可以基于算子做链式计算。 这一层链式计算也支持分布式，支持如filter、join、map、reduce等一套链式计算操作。 后续也将支持更多的计算逻辑。

- DSL层

DSL层提供一套标准的结构化表示语言，可以通过写DSL语句完成AgentFrame与算子的操作，让围绕数据编写大模型应用更具确定性，避免通过自然语言编写的不确定性，使得围绕数据与大模型的应用编程变为确定性应用编程。
### RAG架构
![](https://github.com/eosphoros-ai/DB-GPT/raw/main/assets/RAG-IN-ACTION.jpg#from=url&id=JsJTm&originHeight=1300&originWidth=2272&originalType=binary&ratio=2&rotation=0&showTitle=false&status=done&style=none&title=)
### Agent架构
DB-GPT Agent是一个多Agent框架，目的是提供生产级Agent构建的基础框架能力。我们认为，生产级代理应用程序需要基于数据驱动的决策，并且可以在可控制的工作流中进行编排。
在我们的设计中，提供了一套以Agent为核心，融合多模型管理、RAGs、API调用、可视化、AWEL智能体编排、Text2SQL、意图识别等一系列技术的生产级数据应用开发框架。 
![image.png](https://intranetproxy.alipay.com/skylark/lark/0/2024/png/26456775/1724765648901-d048c6fc-8b08-4623-bc2d-66db8edb893f.png#clientId=u47bade0c-6d5b-4&from=paste&height=376&id=u580c84f4&originHeight=558&originWidth=1076&originalType=binary&ratio=2&rotation=0&showTitle=false&size=862016&status=done&style=none&taskId=ue3fa55ab-171a-4aeb-a7ec-8bcf8e13474&title=&width=725)
如同所示:  在DB-GPT中，Agent是一等公民，其他RAGs、Tools、数据源等都是Agent依赖的资源，包括模型也是一种资源。 
Agent的核心模块主要有Memory、Profile、Planing、Action等模块。 
围绕Agent的核心模块，往上构建多Agent之间的协作能力，协作主要有三种形式。 

1. 单一Agent: 单个Agent有具体任务与目标，不涉及多模型协作。 
2. Auto-Plan: Agent自己制定计划，在多Agent协作时负责路径规划、分工协作等。
3. AWEL: 编排，通过程序编排来实现多智能体的协作。
### 多模型架构
在AIGC应用探索与生产落地中，难以避免直接与模型服务对接，但是目前大模型的推理部署还没有一个事实标准，不断有新的模型发布，也不断有新的训练方法被提出，我们需要花大量的时间来适配多变的底层模型环境，而这在一定程度上制约了AIGC应用的探索和落地。 
![](https://intranetproxy.alipay.com/skylark/lark/0/2024/png/26456775/1724765743005-eb151d72-79a2-4a91-9d85-f46b68bfe031.png#clientId=u47bade0c-6d5b-4&from=paste&id=u26061337&originHeight=1087&originWidth=1439&originalType=url&ratio=2&rotation=0&showTitle=false&status=done&style=none&taskId=u181cfde4-f672-414c-a030-07d40dee916&title=)
SMMF由模型推理层、模型部署层两部分组成。模型推理层对应模型推理框架vLLM、TGI和TensorRT等。模型部署层向下对接推理层，向上提供模型服务能力。 模型部署框架在推理框架之上，提供了多模型实例、多推理框架、多云、自动扩缩容与可观测性等能力。
### 子模块

- [DB-GPT-Hub](https://github.com/eosphoros-ai/DB-GPT-Hub) 通过微调来持续提升Text2SQL效果 
- [DB-GPT-Plugins](https://github.com/eosphoros-ai/DB-GPT-Plugins) DB-GPT 插件仓库, 兼容Auto-GPT
- [GPT-Vis](https://github.com/eosphoros-ai/DB-GPT-Web) 可视化协议 
- [dbgpts](https://github.com/eosphoros-ai/dbgpts)  dbgpts 是官方提供的数据应用仓库, 包含数据智能应用, 智能体编排流程模版, 通用算子等构建在DB-GPT之上的资源。
## 安装
[**教程**](https://www.yuque.com/eosphoros/dbgpt-docs/bex30nsv60ru0fmx)

- [**快速开始**](https://www.yuque.com/eosphoros/dbgpt-docs/ew0kf1plm0bru2ga)
   - [源码安装](https://www.yuque.com/eosphoros/dbgpt-docs/urh3fcx8tu0s9xmb)
   - [Docker安装](https://www.yuque.com/eosphoros/dbgpt-docs/glf87qg4xxcyrp89)
   - [Docker Compose安装](https://www.yuque.com/eosphoros/dbgpt-docs/wwdu11e0v5nkfzin)
- [**使用手册**](https://www.yuque.com/eosphoros/dbgpt-docs/tkspdd0tcy2vlnu4)
   - [知识库](https://www.yuque.com/eosphoros/dbgpt-docs/ycyz3d9b62fccqxh)
   - [数据对话](https://www.yuque.com/eosphoros/dbgpt-docs/gd9hbhi1dextqgbz)
   - [Excel对话](https://www.yuque.com/eosphoros/dbgpt-docs/prugoype0xd2g4bb)
   - [数据库对话](https://www.yuque.com/eosphoros/dbgpt-docs/wswpv3zcm2c9snmg)
   - [报表分析](https://www.yuque.com/eosphoros/dbgpt-docs/vsv49p33eg4p5xc1)
   - [Agents](https://www.yuque.com/eosphoros/dbgpt-docs/pom41m7oqtdd57hm)
- [**进阶教程**](https://www.yuque.com/eosphoros/dbgpt-docs/dxalqb8wsv2xkm5f)
   - [智能体工作流使用](https://www.yuque.com/eosphoros/dbgpt-docs/hcomfb3yrleg7gmq)
   - [智能应用使用](https://www.yuque.com/eosphoros/dbgpt-docs/aiagvxeb86iarq6r)
   - [多模型管理](https://www.yuque.com/eosphoros/dbgpt-docs/huzgcf2abzvqy8uv)
   - [命令行使用](https://www.yuque.com/eosphoros/dbgpt-docs/gd4kgumgd004aly8)
- [**模型服务部署**](https://www.yuque.com/eosphoros/dbgpt-docs/vubxiv9cqed5mc6o)
   - [单机部署](https://www.yuque.com/eosphoros/dbgpt-docs/kwg1ed88lu5fgawb)
   - [集群部署](https://www.yuque.com/eosphoros/dbgpt-docs/gmbp9619ytyn2v1s)
   - [vLLM](https://www.yuque.com/eosphoros/dbgpt-docs/bhy9igdvanx1uluf)
- [**如何Debug**](https://www.yuque.com/eosphoros/dbgpt-docs/eyg0ocbc2ce3q95r)
- [**AWEL**](https://www.yuque.com/eosphoros/dbgpt-docs/zozbzslbfk0m0op5)
- [**FAQ**](https://www.yuque.com/eosphoros/dbgpt-docs/gomtc46qonmyt44l)
## 特性一览

- **私域问答&数据处理&RAG**支持内置、多文件格式上传、插件自抓取等方式自定义构建知识库，对海量结构化，非结构化数据做统一向量存储与检索
- **多数据源&GBI**支持自然语言与Excel、数据库、数仓等多种数据源交互，并支持分析报告。
- **自动化微调**围绕大语言模型、Text2SQL数据集、LoRA/QLoRA/Pturning等微调方法构建的自动化微调轻量框架, 让TextSQL微调像流水线一样方便。详见: [DB-GPT-Hub](https://github.com/eosphoros-ai/DB-GPT-Hub)
- **数据驱动的Agents插件**支持自定义插件执行任务，原生支持Auto-GPT插件模型，Agents协议采用Agent Protocol标准
- **多模型支持与管理**海量模型支持，包括开源、API代理等几十种大语言模型。如LLaMA/LLaMA2、Baichuan、ChatGLM、文心、通义、智谱等。当前已支持如下模型: 
   - 新增支持模型
      - 🔥🔥🔥  [Meta-Llama-3.1-405B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3.1-405B-Instruct)
      - 🔥🔥🔥  [Meta-Llama-3.1-70B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3.1-70B-Instruct)
      - 🔥🔥🔥  [Meta-Llama-3.1-8B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3.1-8B-Instruct)
      - 🔥🔥🔥  [gemma-2-27b-it](https://huggingface.co/google/gemma-2-27b-it)
      - 🔥🔥🔥  [gemma-2-9b-it](https://huggingface.co/google/gemma-2-9b-it)
      - 🔥🔥🔥  [DeepSeek-Coder-V2-Instruct](https://huggingface.co/deepseek-ai/DeepSeek-Coder-V2-Instruct)
      - 🔥🔥🔥  [DeepSeek-Coder-V2-Lite-Instruct](https://huggingface.co/deepseek-ai/DeepSeek-Coder-V2-Lite-Instruct)
      - 🔥🔥🔥  [Qwen2-57B-A14B-Instruct](https://huggingface.co/Qwen/Qwen2-57B-A14B-Instruct)
      - 🔥🔥🔥  [Qwen2-57B-A14B-Instruct](https://huggingface.co/Qwen/Qwen2-57B-A14B-Instruct)
      - 🔥🔥🔥  [Qwen2-72B-Instruct](https://huggingface.co/Qwen/Qwen2-72B-Instruct)
      - 🔥🔥🔥  [Qwen2-7B-Instruct](https://huggingface.co/Qwen/Qwen2-7B-Instruct)
      - 🔥🔥🔥  [Qwen2-1.5B-Instruct](https://huggingface.co/Qwen/Qwen2-1.5B-Instruct)
      - 🔥🔥🔥  [Qwen2-0.5B-Instruct](https://huggingface.co/Qwen/Qwen2-0.5B-Instruct)
      - 🔥🔥🔥  [glm-4-9b-chat](https://huggingface.co/THUDM/glm-4-9b-chat)
      - 🔥🔥🔥  [Phi-3](https://huggingface.co/collections/microsoft/phi-3-6626e15e9585a200d2d761e3)
      - 🔥🔥🔥  [Yi-1.5-34B-Chat](https://huggingface.co/01-ai/Yi-1.5-34B-Chat)
      - 🔥🔥🔥  [Yi-1.5-9B-Chat](https://huggingface.co/01-ai/Yi-1.5-9B-Chat)
      - 🔥🔥🔥  [Yi-1.5-6B-Chat](https://huggingface.co/01-ai/Yi-1.5-6B-Chat)
      - 🔥🔥🔥  [Qwen1.5-110B-Chat](https://huggingface.co/Qwen/Qwen1.5-110B-Chat)
      - 🔥🔥🔥  [Qwen1.5-MoE-A2.7B-Chat](https://huggingface.co/Qwen/Qwen1.5-MoE-A2.7B-Chat)
      - 🔥🔥🔥  [Meta-Llama-3-70B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3-70B-Instruct)
      - 🔥🔥🔥  [Meta-Llama-3-8B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3-8B-Instruct)
      - 🔥🔥🔥  [CodeQwen1.5-7B-Chat](https://huggingface.co/Qwen/CodeQwen1.5-7B-Chat)
      - 🔥🔥🔥  [Qwen1.5-32B-Chat](https://huggingface.co/Qwen/Qwen1.5-32B-Chat)
      - 🔥🔥🔥  [Starling-LM-7B-beta](https://huggingface.co/Nexusflow/Starling-LM-7B-beta)
      - 🔥🔥🔥  [gemma-7b-it](https://huggingface.co/google/gemma-7b-it)
      - 🔥🔥🔥  [gemma-2b-it](https://huggingface.co/google/gemma-2b-it)
      - 🔥🔥🔥  [SOLAR-10.7B](https://huggingface.co/upstage/SOLAR-10.7B-Instruct-v1.0)
      - 🔥🔥🔥  [Mixtral-8x7B](https://huggingface.co/mistralai/Mixtral-8x7B-Instruct-v0.1)
      - 🔥🔥🔥  [Qwen-72B-Chat](https://huggingface.co/Qwen/Qwen-72B-Chat)
      - 🔥🔥🔥  [Yi-34B-Chat](https://huggingface.co/01-ai/Yi-34B-Chat)
   - [更多开源模型](https://www.yuque.com/eosphoros/dbgpt-docs/iqaaqwriwhp6zslc#qQktR)
   - 支持在线代理模型
- [x] [DeepSeek.deepseek-chat](https://platform.deepseek.com/api-docs/)
- [x] [Ollama.API](https://github.com/ollama/ollama/blob/main/docs/api.md)
- [x] [月之暗面.Moonshot](https://platform.moonshot.cn/docs/)
- [x] [零一万物.Yi](https://platform.lingyiwanwu.com/docs)
- [x] [OpenAI·ChatGPT](https://api.openai.com/)
- [x] [百川·Baichuan](https://platform.baichuan-ai.com/)
- [x] [阿里·通义](https://www.aliyun.com/product/dashscope)
- [x] [百度·文心](https://cloud.baidu.com/product/wenxinworkshop?track=dingbutonglan)
- [x] [智谱·ChatGLM](http://open.bigmodel.cn/)
- [x] [讯飞·星火](https://xinghuo.xfyun.cn/)
- [x] [Google·Bard](https://bard.google.com/)
- [x] [Google·Gemini](https://makersuite.google.com/app/apikey)
- **隐私安全**通过私有化大模型、代理脱敏等多种技术保障数据的隐私安全。
- [支持数据源](https://www.yuque.com/eosphoros/dbgpt-docs/rc4r27ybmdwg9472)
## Image
🌐 [AutoDL镜像](https://www.codewithgpu.com/i/eosphoros-ai/DB-GPT/dbgpt)
🌐 [小程序云部署](https://www.yuque.com/eosphoros/dbgpt-docs/ek12ly8k661tbyn8)
### 多语言切换
在.env 配置文件当中，修改LANGUAGE参数来切换使用不同的语言，默认是英文(中文zh, 英文en, 其他语言待补充)
## 使用说明
### 多模型使用
### 数据Agents使用

- [数据Agents](https://www.yuque.com/eosphoros/dbgpt-docs/gwz4rayfuwz78fbq)
## 贡献
## 更加详细的贡献指南请参考[如何贡献](https://github.com/eosphoros-ai/DB-GPT/blob/main/CONTRIBUTING.md)。
这是一个用于数据库的复杂且创新的工具, 我们的项目也在紧急的开发当中, 会陆续发布一些新的feature。如在使用当中有任何具体问题, 优先在项目下提issue, 如有需要, 请联系如下微信，我会尽力提供帮助，同时也非常欢迎大家参与到项目建设中。
## Licence
The MIT License (MIT)
## 引用
如果您发现`DB-GPT`对您的研究或开发有用，请引用以下[论文](https://arxiv.org/abs/2312.17449)：
```
@article{xue2023dbgpt,
      title={DB-GPT: Empowering Database Interactions with Private Large Language Models}, 
      author={Siqiao Xue and Caigao Jiang and Wenhui Shi and Fangyin Cheng and Keting Chen and Hongjun Yang and Zhiping Zhang and Jianshan He and Hongyang Zhang and Ganglin Wei and Wang Zhao and Fan Zhou and Danrui Qi and Hong Yi and Shaodong Liu and Faqiang Chen},
      year={2023},
      journal={arXiv preprint arXiv:2312.17449},
      url={https://arxiv.org/abs/2312.17449}
}
```


# TuGraph
TuGraph图数据库由蚂蚁集团与清华大学联合研发，构建了一套包含图存储、图计算、图学习、图研发平台的完善的图技术体系，支持海量多源的关联数据的实时处理，显著提升数据分析效率，支撑了蚂蚁支付、安全、社交、公益、数据治理等300多个场景应用。拥有业界领先规模的图集群，解决了图数据分析面临的大数据量、高吞吐率和低延迟等重大挑战，是蚂蚁集团金融风控能力的重要基础设施，显著提升了欺诈洗钱等金融风险的实时识别能力和审理分析效率，并面向金融、工业、政务服务等行业客户。TuGraph产品家族中，开源产品包括：TuGraph DB、TuGraph Analytics、OSGraph、ChatTuGraph等。内源产品包括：GeaBase、GeaFlow、GeaLearn、GeaMaker等。

TuGraph企业级图数据管理平台提供对关联数据的复杂、深度分析功能。TuGraph以分布式集群架构，支持海量数据的高吞吐、高可用性、高并发读写和ACID事务操作。通过对数据的分片、分区，支持水平扩展，提供对点、边、属性、拓扑等结构的查询、过滤、索引等功能。TuGraph提供离线、近线、在线的图算法和图学习能力，内置数十种算法，能够对全图、子图、动态图的模式和特征进行处理，通过可视化或数据服务形式与外部数据源交互。此外，TuGraph提供可视化的展示和操作界面，覆盖图研发和服务的全生命周期，支持主流的图查询语言，提供便捷的访问和开发接口，能够与外部多模数据源进行导入导出、存量/增量/批量更新和备份。TuGraph还提供精美和实用的图生产环境管理监控，满足企业用户的技术和业务应用需要。

TuGraph在金融风控方面的应用实践主要包括个人信贷业务、反欺诈、洗钱路径追踪等问题。利用多维交叉关联信息深度刻画申请和交易行为，识别多种复杂、规模化、隐蔽性的欺诈网络和洗钱网络；结合聚类分析、风险传播等算法，实时计算用户的风险评分，在风险行为发生前预先识别，帮助金融机构提升效率、降低风险。基于TuGraph企业级图数据管理平台，蚂蚁集团增加反欺诈稽核金额6%，反洗钱风险审理分析效率提升90%。每天计算近10亿用户大约200亿左右边关系，对疑似团伙类犯罪风险识别能力提高近10倍。此外，为某银行提供的信贷图平台提升了13%的风控模型区分度；为某银行完成的信用卡申请团伙欺诈分析方案，运算时间缩短至原有的1/60；为某银行搭建的企业风险图平台，在对小微企业评级放贷问题中，担保圈识别准确率达到90%以上。


## 1. TuGraph DB

### 1.1 简介
TuGraph DB 是支持大数据容量、低延迟查找和快速图分析功能的高效图数据库。TuGraph社区版于2022年9月开源，提供了完整的图数据库基础功能和成熟的产品设计（如ACID兼容的事务、编程API和配套工具等），适用于单实例部署。社区版支持TB级别的数据规模，为用户管理和分析复杂关联数据提供了高效、易用、可靠的平台，是学习TuGraph和实现小型项目的理想选择。

### 1.2 TuGraph特性
TuGraph是支持大数据量、低延迟查找和快速图分析功能的高效图数据库。TuGraph也是基于磁盘的数据库，支持存储多达数十TB的数据。TuGraph提供多种API，使用户能够轻松构建应用程序，并使其易于扩展和优化。

它具有如下功能特征：

* 属性图模型
* 实时增删查改
* 多重图（点间允许多重边）
* 多图（大图与多个子图）
* 完善的ACID事务处理，隔离级别为可串行化（serializable）
* 点边索引
* 混合事务和分析处理（HTAP），支持图查询、图分析、图学习
* 主流图查询语言（OpenCypher、ISO GQL等）
* 支持OLAP API，内置30多种图分析算法
* 基于C++/Python的存储过程，含事务内并行Traversal API
* 提供图可视化工具
* 在性能和可扩展性方面的支持：
* 千万点/秒的高吞吐率
* TB级大容量
* 高可用性支持
* 高性能批量导入
* 在线/离线的备份恢复


主要功能：

- 标签属性图模型
- 完善的 ACID 事务处理
- 内置 34 图分析算法
- 支持全文/主键/二级索引
- OpenCypher 图查询语言
- 基于 C++/Python 的存储过程

性能和可扩展性：

- LDBC SNB世界记录保持者 (2022/9/1 https://ldbcouncil.org/benchmarks/snb/)
- 支持存储多达数十TB的数据
- 每秒访问数百万个顶点
- 快速批量导入

TuGraph DB的文档在[链接](https://tugraph-db.readthedocs.io/zh_CN/latest)，欢迎访问我们的[官网](https://www.tugraph.org)。

### 1.3 快速上手

一个简单的方法是使用docker进行设置，可以在[DockerHub](https://hub.docker.com/u/tugraph)中找到, 名称为`tugraph/tugraph-runtime-[os]:[tugraph version]`,
例如， `tugraph/tugraph-runtime-centos7:3.3.0`。

更多详情请参考 [快速上手文档](./docs/zh-CN/source/3.quick-start/1.preparation.md) 和 [业务开发指南](./docs/zh-CN/source/development_guide.md).

### 1.4 从源代码编译

建议在Linux系统中构建TuGraph DB，Docker环境是个不错的选择。如果您想设置一个新的环境，请参考[Dockerfile](ci/images).

以下是编译TuGraph DB的步骤：

1. 如果需要web接口运行`deps/build_deps.sh`，不需要web接口则跳过此步骤
2. 根据容器系统信息执行`cmake .. -DOURSYSTEM=centos`或者`cmake .. -DOURSYSTEM=ubuntu`
3. `make`
4. `make package` 或者 `cpack --config CPackConfig.cmake`

示例：`tugraph/tugraph-compile-centos7`Docker环境

```bash
$ git clone --recursive https://github.com/TuGraph-family/tugraph-db.git
$ cd tugraph-db
$ deps/build_deps.sh
$ mkdir build && cd build
$ cmake .. -DOURSYSTEM=centos7
$ make
$ make package
```

### 1.5 开发

我们已为在DockerHub中编译准备了环境docker镜像，可以帮助开发人员轻松入门，名称为 `tugraph/tugraph-compile-[os]:[compile version]`, 例如， `tugraph/tugraph-compile-centos7:1.1.0`。

## 2. TuGraph Analytics

### 2.1 介绍
**TuGraph Analytics** (别名：GeaFlow) 是蚂蚁集团开源的[**性能世界一流**](https://ldbcouncil.org/benchmarks/snb-bi/)的OLAP图数据库，支持万亿级图存储、图表混合处理、实时图计算、交互式图分析等核心能力，目前广泛应用于数仓加速、金融风控、知识图谱以及社交网络等场景。

关于GeaFlow更多介绍请参考：[GeaFlow介绍文档](docs/docs-cn/introduction.md)

GeaFlow设计论文参考：[GeaFlow: A Graph Extended and Accelerated Dataflow System](https://dl.acm.org/doi/abs/10.1145/3589771)

### 2.2 起源

早期的大数据分析主要以离线处理为主，以Hadoop为代表的技术栈很好的解决了大规模数据的分析问题。然而数据处理的时效性不足，
很难满足高实时需求的场景。以Storm为代表的流式计算引擎的出现则很好的解决了数据实时处理的问题，提高了数据处理的时效性。
然而，Storm本身不提供状态管理的能力， 对于聚合等有状态的计算显得无能为力。Flink
的出现很好的弥补了这一短板，通过引入状态管理以及Checkpoint机制，实现了高效的有状态流计算能力。

随着数据实时处理场景的丰富，尤其是在实时数仓场景下，实时关系运算(即Stream Join)
越来越多的成为数据实时化的难点。Flink虽然具备优秀的状态管理能和出色的性能，然而在处理Join运算，尤其是3度以上Join时，
性能瓶颈越来越明显。由于需要在Join两端存放各个输入的数据状态，当Join变多时，状态的数据量急剧扩大，性能也变的难以接受。
产生这个问题的本质原因是Flink等流计算系统以表作为数据模型，而表模型本身是一个二维结构，不包含关系的定义和关系的存储，
在处理关系运算时只能通过Join运算方式实现，成本很高。

在蚂蚁的大数据应用场景中，尤其是金融风控、实时数仓等场景下，存在大量Join运算，如何提高Join
的时效性和性能成为我们面临的重要挑战，为此我们引入了图模型。图模型是一种以点边结构描述实体关系的数据模型，在图模型里面，点代表实体，
边代表关系，数据存储层面点边存放在一起。因此，图模型天然定义了数据的关系同时存储层面物化了点边关系。基于图模型，我们实现了新一代实时计算
引擎GeaFlow，很好的解决了复杂关系运算实时化的问题。目前GeaFlow已广泛应用于数仓加速、金融风控、知识图谱以及社交网络等场景。

### 2.3 特性

* 分布式实时图计算
* 图表混合处理（SQL+GQL语言）
* 统一流批图计算
* 万亿级图原生存储
* 交互式图分析
* 高可用和Exactly Once语义
* 高阶API算子开发
* UDF/图算法/Connector插件支持
* 一站式图研发平台
* 云原生部署

### 2.4 快速上手

1. 准备Git、JDK8、Maven、Docker环境。
2. 下载源码：`git clone https://github.com/TuGraph-family/tugraph-analytics`
3. 项目构建：`mvn clean install -DskipTests`
4. 测试任务：`./bin/gql_submit.sh --gql geaflow/geaflow-examples/gql/loop_detection.sql`
3. 构建镜像：`./build.sh --all`
4. 启动容器：`docker run -d --name geaflow-console -p 8888:8888 geaflow-console:0.1`

更多详细内容请参考：[快速上手文档](docs/docs-cn/quick_start.md)。

### 2.5 开发手册

GeaFlow支持DSL和API两套编程接口，您既可以通过GeaFlow提供的类SQL扩展语言SQL+ISO/GQL进行流图计算作业的开发，也可以通过GeaFlow的高阶API编程接口通过Java语言进行应用开发。
* DSL应用开发：[DSL开发文档](docs/docs-cn/application-development/dsl/overview.md)
* API应用开发：[API开发文档](docs/docs-cn/application-development/api/guid.md)


### 2.6 技术架构

GeaFlow整体架构如下所示：

![GeaFlow架构](../static/img/geaflow_arch_new.png)

* [DSL层](./principle/dsl_principle.md)：即语言层。GeaFlow设计了SQL+GQL的融合分析语言，支持对表模型和图模型统一处理。
* [Framework层](./principle/framework_principle.md)：即框架层。GeaFlow设计了面向Graph和Stream的两套API支持流、批、图融合计算，并实现了基于Cycle的统一分布式调度模型。
* [State层](./principle/state_principle.md)：即存储层。GeaFlow设计了面向Graph和KV的两套API支持表数据和图数据的混合存储，整体采用了Sharing Nothing的设计，并支持将数据持久化到远程存储。
* [Console平台](./principle/console_principle.md)：GeaFlow提供了一站式图研发平台，实现了图数据的建模、加工、分析能力，并提供了图作业的运维管控支持。
* **执行环境**：GeaFlow可以运行在多种异构执行环境，如K8S、Ray以及本地模式。

### 2.7 应用场景

#### 2.7.1 实时数仓加速
数仓场景存在大量Join运算，在DWD层往往需要将多张表展开成一张大宽表，以加速后续查询。当Join的表数量变多时，传统的实时计算引擎很难
保证Join的时效性和性能，这也成为目前实时数仓领域一个棘手的问题。基于GeaFlow的实时图计算引擎，可以很好的解决这方面的问题。
GeaFlow以图作为数据模型，替代DWD层的宽表，可以实现数据实时构图，同时在查询阶段利用图的点边物化特性，可以极大加速关系运算的查询。

#### 2.7.2 实时归因分析
在信息化的大背景下，对用户行为进行渠道归因和路径分析是流量分析领域中的核心所在。通过实时计算用户的有效行为路径，构建出完整的转化路径，能够快速帮助业务看清楚产品的价值，帮助运营及时调整运营思路。实时归因分析的核心要点是准确性和实效性。准确性要求在成本可控下保证用户行为路径分析的准确性;实效性则要求计算的实时性足够高，才能快速帮助业务决策。
基于GeaFlow流图计算引擎的能力可以很好的满足归因分析的准确性和时效性要求。如下图所示：
![归因分析](../static/img/guiyin_analysis.png)
GeaFlow首先通过实时构图将用户行为日志转换成用户行为拓扑图，以用户作为图中的点，与其相关的每个行为构建成从该用户指向埋点页面的一条边.然后利用流图计算能力分析提前用户行为子图，在子图上基于归因路径匹配的规则进行匹配计算得出该成交行为相应用户的归因路径，并输出到下游系统。

#### 2.7.3 实时反套现
在信贷风控的场景下，如何进行信用卡反套现是一个典型的风控诉求。基于现有的套现模式分析，可以看到套现是一个环路子图，如何快速，高效在大图中快速判定套现，将极大的增加风险的识别效率。以下图为例，通过将实时交易流、转账流等输入数据源转换成实时交易图，然后根据风控策略对用户交易行为做图特征分析，比如环路检查等特征计算，实时提供给决策和监控平台进行反套现行为判定。通过GeaFlow实时构图和实时图计算能力，可以快速发现套现等异常交易行为，极大降低平台风险。
![实时反套现](../static/img/fantaoxian.png)



## 3. OSGraph

**OSGraph (Open Source Graph)** 是一个开源图谱关系洞察工具，基于GitHub开源数据全域图谱，实现开发者行为、项目社区生态的分析洞察。可以为开发者、项目Owner、开源布道师、社区运营等提供简洁直观的开源数据视图，帮助你和你的项目制作专属的开源名片、寻求契合的开发伙伴、挖掘深度的社区价值。


### 3.1 产品地址

**[https://osgraph.com](https://osgraph.com)**


### 3.2 快速开始

本地启动测试请参考：[OSGraph部署文档](docs/zh-CN/DeveloperManual.md)


### 3.3 功能介绍

当前产品默认提供了6张开源数据图谱供大家体验，包含项目类图谱3个（贡献、生态、社区）、开发类3个（活动、伙伴、兴趣）。


#### 3.3.1  项目贡献图谱

**发现项目核心贡献**：根据项目开发者研发活动信息（Issue、PR、Commit、CR等），找到项目核心贡献者。

**Q**：我想看看给Apache Spark项目写代码的都有谁？

**A**：选择“项目贡献图谱” - 搜索spark - 选择apache/spark。可以看到HyukjinKwon、dongjoon-hyun等核心贡献者，另外还一不小心捉到两个“显眼包”，AmplabJenkins、SparkQA这两个只参与CodeReview的机器人账号。

![](docs/img/spark-contrib.png)


#### 3.3.2 项目生态图谱

**洞察项目生态伙伴**：提取项目间的开发活动、组织等关联信息，构建项目核心生态关系。

**Q**：最近很火的开源大模型Llama3周边生态大致是什么样的？

**A**：选择“项目生态图谱” - 搜索llama3 - 选择meta-llama3/llama3。可以看到pytorch、tensorflow、transformers等知名AI项目，当然还有上科技头条的llama.cpp。比较惊喜的发现是ray竟然和llama3有不少公共开发者，可以深度挖掘一下。

![](docs/img/llama3-eco.png)



#### 3.3.3 项目社区图谱

**分析项目社区分布**：根据项目的开发活动、开发者组织等信息，提取项目核心开发者社区分布。

**Q**：大数据引擎Flink发展这么多年后的社区现状如何？

**A**：选择“项目社区图谱” - 搜索flink - 选择apache/flink。可以看到项目关注者主要来自中、美、德三国，而Alibaba组织是代码贡献的中坚力量。

![](docs/img/flink-comm.png)



#### 3.3.4 开发活动图谱

**展示个人开源贡献**：根据开发者研发活动信息（Issue、PR、Commit、CR等），找到参与的核心项目。

**Q**：大神Linus Torvalds最近在参与哪些开源项目？

**A**：选择“开发活动图谱” - 搜索torvalds。果然linux项目是torvalds的主要工作，不过llvm、mody、libgit2也有所参与，同时也看到他在subsurface这种“潜水日志管理工具”上的大量贡献，果然大佬的爱好都很广泛。

![](docs/img/torvalds-act.png)



#### 3.3.5 开源伙伴图谱

**寻找个人开源伙伴**：找到开发者在开源社区中，与之协作紧密的其他开发者。

**Q**：我想知道在开源社区有没有和我志同道合的人？

**A**：选择“开发伙伴图谱” - 搜索我的ID。让我震惊的是有那么多陌生人和我关注了同一批项目，这不得找机会认识一下，说不定就能找到新朋友了。而和我合作PR的人基本上都是我认识的朋友和同事，继续探索一下朋友们的开源伙伴，开源社区的“六度人脉”不就来了么。

![](docs/img/fanzhidongyzby-part.png)



#### 3.3.6 开源兴趣图谱

**挖掘个人开源兴趣**：根据参与的项目主题、标签等信息，分析开发者技术领域与兴趣。

**Q**：GitHub上最活跃的开发者对什么技术感兴趣？

**A**：选择“开源兴趣图谱” - 搜索sindresorhus（[GitHub用户榜](https://gitstar-ranking.com) No.1）。整体来看sindresorhus对node、npm、js很感兴趣，另外他发起的awesome项目足足30W星，令人咋舌！当前的开源兴趣数据主要来自项目有限的标签信息，后续借助AI技术可能会有更好的展现。

![](docs/img/sindresorhus-intr.png)


### 3.4 未来规划

未来将会有更多有趣的图谱和功能加入到OSGraph：

* 简单灵活的API设计，让图谱无限扩展。
* 自由高效的画布交互，无限探索数据价值。
* 图谱URL支持嵌入Markdown，制作我的开源名片。
* 基于AI技术的项目主题标签分析。
* 多人多项目联合分析，图谱洞察一键可达。
* 更丰富的数据展示与多维分析。
* **更多功能，与你携手共建……**



## 4. ChatTuGraph

ChatTuGraph通过AI技术为TuGraph赋能，可以为图业务研发效能、图产品解决方案、图数据智能分析、图任务自动管控等领域带来更丰富的应用场景。
目前ChatTuGraph通过图语言语料生成，借助大模型微调技术实现了自然语言的图数据分析，构建Graph RAG基于知识图谱实现检索增强生成，以降低大模型的推理幻觉，以及通过多智能体技术（Multiple Agents System）实现图数据上的AIGC、智能化等能力。





# OSGraph开源图谱数据

## 1. DB-GPT开源图谱

### 1.1 DB-GPT项目生态图谱
Entities:
(eosphoros-ai/DB-GPT#github_repo)
(chatchat-space/Langchain-Chatchat#github_repo)
(hiyouga/LLaMA-Factory#github_repo)
(lm-sys/FastChat#github_repo)
(langchain-ai/langchain#github_repo)
(eosphoros-ai/DB-GPT-Hub#github_repo)
(THUDM/ChatGLM-6B#github_repo)
(langgenius/dify#github_repo)
(vllm-project/vllm#github_repo)
(QwenLM/Qwen#github_repo)
(PaddlePaddle/PaddleOCR#github_repo)
(vllm-project#github_organization)
(eosphoros-ai#github_organization)
(PaddlePaddle#github_organization)
(QwenLM#github_organization)
(THUDM#github_organization)
(lm-sys#github_organization)
(chatchat-space#github_organization)
(langchain-ai#github_organization)
(langgenius#github_organization)

Relationships:
(eosphoros-ai/DB-GPT#common_developer#chatchat-space/Langchain-Chatchat#common_developer count 82)
(eosphoros-ai/DB-GPT#common_developer#hiyouga/LLaMA-Factory#common_developer count 45)
(eosphoros-ai/DB-GPT#common_developer#lm-sys/FastChat#common_developer count 39)
(eosphoros-ai/DB-GPT#common_developer#langchain-ai/langchain#common_developer count 37)
(eosphoros-ai/DB-GPT#common_developer#eosphoros-ai/DB-GPT-Hub#common_developer count 37)
(eosphoros-ai/DB-GPT#common_developer#THUDM/ChatGLM-6B#common_developer count 31)
(eosphoros-ai/DB-GPT#common_developer#langgenius/dify#common_developer count 30)
(eosphoros-ai/DB-GPT#common_developer#vllm-project/vllm#common_developer count 27)
(eosphoros-ai/DB-GPT#common_developer#QwenLM/Qwen#common_developer count 26)
(eosphoros-ai/DB-GPT#common_developer#PaddlePaddle/PaddleOCR#common_developer count 24)
(vllm-project/vllm#belong_to#vllm-project#belong_to)
(eosphoros-ai/DB-GPT#belong_to#eosphoros-ai#belong_to)
(eosphoros-ai/DB-GPT-Hub#belong_to#eosphoros-ai#belong_to)
(PaddlePaddle/PaddleOCR#belong_to#PaddlePaddle#belong_to)
(QwenLM/Qwen#belong_to#QwenLM#belong_to)
(THUDM/ChatGLM-6B#belong_to#THUDM#belong_to)
(lm-sys/FastChat#belong_to#lm-sys#belong_to)
(chatchat-space/Langchain-Chatchat#belong_to#chatchat-space#belong_to)
(langchain-ai/langchain#belong_to#langchain-ai#belong_to)
(langgenius/dify#belong_to#langgenius#belong_to)

### 1.2 DB-GPT项目社区图谱
Entities:
(Student#company)
(eosphoros-ai/DB-GPT#github_repo)
(@macacajs @alibaba @nodejs#company)
(@performgroup#company)
(@开源探索者#company)
(Ant Group#company)
(China#country)
(United States#country)
(India#country)
(Germany#country)
(South Korea#country)
(chinmay7016#github_user)
(Killer2OP#github_user)
(xudafeng#github_user)
(peter-wangxu#github_user)
(Aralhi#github_user)
(csunny#github_user)
(SuperSupeng#github_user)
(xuyuan23#github_user)
(wangzaistone#github_user)
(krzysztof-magosa#github_user)

Relationships:
(Student#PR#eosphoros-ai/DB-GPT#PR count 2)
(@macacajs @alibaba @nodejs#PR#eosphoros-ai/DB-GPT#PR count 3)
(@performgroup#PR#eosphoros-ai/DB-GPT#PR count 1)
(@开源探索者#PR#eosphoros-ai/DB-GPT#PR count 1)
(Ant Group#PR#eosphoros-ai/DB-GPT#PR count 1)
(China#Star#eosphoros-ai/DB-GPT#Star count 1612)
(United States#Star#eosphoros-ai/DB-GPT#Star count 277)
(India#Star#eosphoros-ai/DB-GPT#Star count 73)
(Germany#Star#eosphoros-ai/DB-GPT#Star count 56)
(South Korea#Star#eosphoros-ai/DB-GPT#Star count 43)
(chinmay7016#belong_to#India#belong_to)
(Killer2OP#belong_to#India#belong_to)
(xudafeng#belong_to#China#belong_to)
(peter-wangxu#belong_to#China#belong_to)
(Aralhi#belong_to#China#belong_to)
(csunny#belong_to#China#belong_to)
(SuperSupeng#belong_to#China#belong_to)
(xuyuan23#belong_to#China#belong_to)
(wangzaistone#belong_to#China#belong_to)
(SuperSupeng#belong_to#@开源探索者#belong_to)
(peter-wangxu#belong_to#Ant Group#belong_to)
(krzysztof-magosa#belong_to#@performgroup#belong_to)
(xudafeng#belong_to#@macacajs @alibaba @nodejs#belong_to)
(wangzaistone#belong_to#Student#belong_to)
### 1.3 DB-GPT项目贡献图谱
Entities:
(Aries-ckt#github_user)
(eosphoros-ai/DB-GPT#github_repo)
(fangyinc#github_user)
(2976151305#github_user)
(csunny#github_user)
(lcxadml#github_user)
(Aralhi#github_user)
(yhjun1026#github_user)
(xuyuan23#github_user)
(jsRuner#github_user)
(likenamehaojie#github_user)
(ASTLY123#github_user)
(lordk911#github_user)
(fayfox#github_user)
(xiuzhu9527#github_user)
(ketingli1#github_user)
(yihong0618#github_user)
(huicewang#github_user)
(njhouse365#github_user)
(liujie316316#github_user)
(alexlovy#github_user)
(jiangshengdev#github_user)
(zhonggegege#github_user)
(bzr1#github_user)
(github-actions[bot]#github_user)
(vahede#github_user)
(aaadrain#github_user)
(eigen2017#github_user)

Relationships:
(Aries-ckt#push#eosphoros-ai/DB-GPT#push count 34)
(fangyinc#push#eosphoros-ai/DB-GPT#push count 15)
(2976151305#push#eosphoros-ai/DB-GPT#push count 13)
(csunny#push#eosphoros-ai/DB-GPT#push count 12)
(lcxadml#push#eosphoros-ai/DB-GPT#push count 7)
(Aralhi#push#eosphoros-ai/DB-GPT#push count 3)
(yhjun1026#push#eosphoros-ai/DB-GPT#push count 2)
(xuyuan23#push#eosphoros-ai/DB-GPT#push count 1)
(fangyinc#open_pr#eosphoros-ai/DB-GPT#open_pr count 12)
(Aries-ckt#open_pr#eosphoros-ai/DB-GPT#open_pr count 7)
(jsRuner#open_pr#eosphoros-ai/DB-GPT#open_pr count 6)
(csunny#open_pr#eosphoros-ai/DB-GPT#open_pr count 4)
(2976151305#open_pr#eosphoros-ai/DB-GPT#open_pr count 4)
(likenamehaojie#open_pr#eosphoros-ai/DB-GPT#open_pr count 2)
(ASTLY123#open_pr#eosphoros-ai/DB-GPT#open_pr count 1)
(lordk911#open_pr#eosphoros-ai/DB-GPT#open_pr count 1)
(fayfox#open_pr#eosphoros-ai/DB-GPT#open_pr count 1)
(xiuzhu9527#open_pr#eosphoros-ai/DB-GPT#open_pr count 1)
(csunny#code_review#eosphoros-ai/DB-GPT#code_review count 45)
(Aries-ckt#code_review#eosphoros-ai/DB-GPT#code_review count 31)
(fangyinc#code_review#eosphoros-ai/DB-GPT#code_review count 20)
(jsRuner#code_review#eosphoros-ai/DB-GPT#code_review count 13)
(yhjun1026#code_review#eosphoros-ai/DB-GPT#code_review count 6)
(2976151305#code_review#eosphoros-ai/DB-GPT#code_review count 2)
(xiuzhu9527#code_review#eosphoros-ai/DB-GPT#code_review count 1)
(ketingli1#code_review#eosphoros-ai/DB-GPT#code_review count 1)
(Aralhi#code_review#eosphoros-ai/DB-GPT#code_review count 1)
(yihong0618#code_review#eosphoros-ai/DB-GPT#code_review count 1)
(huicewang#open_issue#eosphoros-ai/DB-GPT#open_issue count 5)
(jsRuner#open_issue#eosphoros-ai/DB-GPT#open_issue count 3)
(njhouse365#open_issue#eosphoros-ai/DB-GPT#open_issue count 3)
(csunny#open_issue#eosphoros-ai/DB-GPT#open_issue count 3)
(liujie316316#open_issue#eosphoros-ai/DB-GPT#open_issue count 3)
(alexlovy#open_issue#eosphoros-ai/DB-GPT#open_issue count 2)
(jiangshengdev#open_issue#eosphoros-ai/DB-GPT#open_issue count 2)
(zhonggegege#open_issue#eosphoros-ai/DB-GPT#open_issue count 2)
(bzr1#open_issue#eosphoros-ai/DB-GPT#open_issue count 2)
(ASTLY123#open_issue#eosphoros-ai/DB-GPT#open_issue count 2)
(github-actions[bot]#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 29)
(Aries-ckt#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 19)
(fangyinc#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 12)
(csunny#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 4)
(jsRuner#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 2)
(Aralhi#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 1)
(ketingli1#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 1)
(vahede#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 1)
(aaadrain#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 1)
(eigen2017#comment_issue#eosphoros-ai/DB-GPT#comment_issue count 1)

## 2. TuGraph DB开源图谱
### 2.1 TuGraph DB项目生态图谱
Entities:
(TuGraph-family/tugraph-db#github_repo)
(vesoft-inc/nebula#github_repo)
(PaddlePaddle/Paddle#github_repo)
(apache/brpc#github_repo)
(TuGraph-family/tugraph-web#github_repo)
(TuGraph-family/tugraph-db-client-java#github_repo)
(alibaba/GraphScope#github_repo)
(ClickHouse/ClickHouse#github_repo)
(TuGraph-family/fma-common#github_repo)
(vesoft-inc/nebula-docs-cn#github_repo)
(eosphoros-ai/DB-GPT#github_repo)
(eosphoros-ai#github_organization)
(yandex#github_organization)
(alibaba#github_organization)
(TuGraph-family#github_organization)
(baidu#github_organization)
(apache#github_organization)
(vesoft-inc#github_organization)

Relationships:
(TuGraph-family/tugraph-db#common_developer#vesoft-inc/nebula#common_developer count 10)
(TuGraph-family/tugraph-db#common_developer#PaddlePaddle/Paddle#common_developer count 9)
(TuGraph-family/tugraph-db#common_developer#apache/brpc#common_developer count 7)
(TuGraph-family/tugraph-db#common_developer#TuGraph-family/tugraph-web#common_developer count 7)
(TuGraph-family/tugraph-db#common_developer#TuGraph-family/tugraph-db-client-java#common_developer count 7)
(TuGraph-family/tugraph-db#common_developer#alibaba/GraphScope#common_developer count 6)
(TuGraph-family/tugraph-db#common_developer#ClickHouse/ClickHouse#common_developer count 6)
(TuGraph-family/tugraph-db#common_developer#TuGraph-family/fma-common#common_developer count 6)
(TuGraph-family/tugraph-db#common_developer#vesoft-inc/nebula-docs-cn#common_developer count 6)
(TuGraph-family/tugraph-db#common_developer#eosphoros-ai/DB-GPT#common_developer count 6)
(eosphoros-ai/DB-GPT#belong_to#eosphoros-ai#belong_to)
(ClickHouse/ClickHouse#belong_to#yandex#belong_to)
(alibaba/GraphScope#belong_to#alibaba#belong_to)
(TuGraph-family/tugraph-db#belong_to#TuGraph-family#belong_to)
(TuGraph-family/tugraph-web#belong_to#TuGraph-family#belong_to)
(TuGraph-family/fma-common#belong_to#TuGraph-family#belong_to)
(TuGraph-family/tugraph-db-client-java#belong_to#TuGraph-family#belong_to)
(PaddlePaddle/Paddle#belong_to#baidu#belong_to)
(apache/brpc#belong_to#apache#belong_to)
(vesoft-inc/nebula#belong_to#vesoft-inc#belong_to)
(vesoft-inc/nebula-docs-cn#belong_to#vesoft-inc#belong_to)
### 2.2 TuGraph DB项目社区图谱
Entities:
(AntGroup#company)
(TuGraph-family/tugraph-db#github_repo)
(SEU#company)
(@alibaba#company)
(666#company)
(FMA#company)
(China#country)
(United States#country)
(Canada#country)
(Germany#country)
(India#country)
(fanzhidongyzby#github_user)
(hjk41#github_user)
(ljcui#github_user)
(yangyang233333#github_user)
(jasinliu#github_user)
(antkiller996#github_user)

Relationships:
(AntGroup#PR#TuGraph-family/tugraph-db#PR count 7)
(SEU#PR#TuGraph-family/tugraph-db#PR count 5)
(@alibaba#PR#TuGraph-family/tugraph-db#PR count 2)
(666#PR#TuGraph-family/tugraph-db#PR count 1)
(FMA#PR#TuGraph-family/tugraph-db#PR count 6)
(China#Star#TuGraph-family/tugraph-db#Star count 329)
(United States#Star#TuGraph-family/tugraph-db#Star count 25)
(Canada#Star#TuGraph-family/tugraph-db#Star count 6)
(Germany#Star#TuGraph-family/tugraph-db#Star count 6)
(India#Star#TuGraph-family/tugraph-db#Star count 5)
(fanzhidongyzby#belong_to#China#belong_to)
(hjk41#belong_to#China#belong_to)
(ljcui#belong_to#China#belong_to)
(yangyang233333#belong_to#China#belong_to)
(jasinliu#belong_to#China#belong_to)
(antkiller996#belong_to#China#belong_to)
(yangyang233333#belong_to#666#belong_to)
(fanzhidongyzby#belong_to#@alibaba#belong_to)
(jasinliu#belong_to#SEU#belong_to)
(hjk41#belong_to#FMA#belong_to)
(antkiller996#belong_to#AntGroup#belong_to)
### 2.3 TuGraph DB项目贡献图谱
Entities:
(qishipengqsp#github_user)
(TuGraph-family/tugraph-db#github_repo)
(lipanpan03#github_user)
(ljcui#github_user)
(jiazhenjiang#github_user)
(spasserby#github_user)
(wangtao9#github_user)
(CLAassistant#github_user)
(GongChangYan#github_user)
(liukaifei#github_user)
(AK-tu#github_user)
(huzhihao1#github_user)
(J4ckycjl#github_user)
(pengpeng176#github_user)
(liaoxj#github_user)
(xizheyin#github_user)
(gtahoo#github_user)
(github-actions[bot]#github_user)

Relationships:
(qishipengqsp#push#TuGraph-family/tugraph-db#push count 12)
(lipanpan03#push#TuGraph-family/tugraph-db#push count 2)
(ljcui#push#TuGraph-family/tugraph-db#push count 1)
(jiazhenjiang#open_pr#TuGraph-family/tugraph-db#open_pr count 5)
(ljcui#open_pr#TuGraph-family/tugraph-db#open_pr count 4)
(lipanpan03#open_pr#TuGraph-family/tugraph-db#open_pr count 3)
(qishipengqsp#open_pr#TuGraph-family/tugraph-db#open_pr count 3)
(spasserby#open_pr#TuGraph-family/tugraph-db#open_pr count 1)
(qishipengqsp#code_review#TuGraph-family/tugraph-db#code_review count 9)
(ljcui#code_review#TuGraph-family/tugraph-db#code_review count 7)
(lipanpan03#code_review#TuGraph-family/tugraph-db#code_review count 5)
(jiazhenjiang#code_review#TuGraph-family/tugraph-db#code_review count 4)
(spasserby#code_review#TuGraph-family/tugraph-db#code_review count 2)
(wangtao9#code_review#TuGraph-family/tugraph-db#code_review count 2)
(CLAassistant#code_review#TuGraph-family/tugraph-db#code_review count 1)
(GongChangYan#code_review#TuGraph-family/tugraph-db#code_review count 1)
(ljcui#open_issue#TuGraph-family/tugraph-db#open_issue count 2)
(liukaifei#open_issue#TuGraph-family/tugraph-db#open_issue count 2)
(AK-tu#open_issue#TuGraph-family/tugraph-db#open_issue count 1)
(huzhihao1#open_issue#TuGraph-family/tugraph-db#open_issue count 1)
(J4ckycjl#open_issue#TuGraph-family/tugraph-db#open_issue count 1)
(pengpeng176#open_issue#TuGraph-family/tugraph-db#open_issue count 1)
(liaoxj#open_issue#TuGraph-family/tugraph-db#open_issue count 1)
(xizheyin#open_issue#TuGraph-family/tugraph-db#open_issue count 1)
(ljcui#comment_issue#TuGraph-family/tugraph-db#comment_issue count 5)
(gtahoo#comment_issue#TuGraph-family/tugraph-db#comment_issue count 1)
(github-actions[bot]#comment_issue#TuGraph-family/tugraph-db#comment_issue count 1)
(liukaifei#comment_issue#TuGraph-family/tugraph-db#comment_issue count 1)
## 3. TuGraph Analytics开源图谱
### 3.1 TuGraph Analytics项目生态图谱
Entities:
(TuGraph-family/tugraph-analytics#github_repo)
(TuGraph-family/tugraph-db#github_repo)
(apache/flink#github_repo)
(StarRocks/starrocks#github_repo)
(apache/incubator-hugegraph#github_repo)
(prisma/prisma#github_repo)
(antvis/G6VP#github_repo)
(apache/kyuubi#github_repo)
(alibaba/nacos#github_repo)
(alibaba/GraphScope#github_repo)
(apache/doris#github_repo)
(antvis#github_organization)
(StarRocks#github_organization)
(apache#github_organization)
(TuGraph-family#github_organization)
(prisma#github_organization)
(alibaba#github_organization)

Relationships:
(TuGraph-family/tugraph-analytics#common_developer#TuGraph-family/tugraph-db#common_developer count 5)
(TuGraph-family/tugraph-analytics#common_developer#apache/flink#common_developer count 4)
(TuGraph-family/tugraph-analytics#common_developer#StarRocks/starrocks#common_developer count 4)
(TuGraph-family/tugraph-analytics#common_developer#apache/incubator-hugegraph#common_developer count 3)
(TuGraph-family/tugraph-analytics#common_developer#prisma/prisma#common_developer count 3)
(TuGraph-family/tugraph-analytics#common_developer#antvis/G6VP#common_developer count 3)
(TuGraph-family/tugraph-analytics#common_developer#apache/kyuubi#common_developer count 3)
(TuGraph-family/tugraph-analytics#common_developer#alibaba/nacos#common_developer count 3)
(TuGraph-family/tugraph-analytics#common_developer#alibaba/GraphScope#common_developer count 3)
(TuGraph-family/tugraph-analytics#common_developer#apache/doris#common_developer count 3)
(antvis/G6VP#belong_to#antvis#belong_to)
(StarRocks/starrocks#belong_to#StarRocks#belong_to)
(apache/flink#belong_to#apache#belong_to)
(apache/doris#belong_to#apache#belong_to)
(apache/kyuubi#belong_to#apache#belong_to)
(apache/incubator-hugegraph#belong_to#apache#belong_to)
(TuGraph-family/tugraph-db#belong_to#TuGraph-family#belong_to)
(TuGraph-family/tugraph-analytics#belong_to#TuGraph-family#belong_to)
(prisma/prisma#belong_to#prisma#belong_to)
(alibaba/nacos#belong_to#alibaba#belong_to)
(alibaba/GraphScope#belong_to#alibaba#belong_to)
### 3.2 TuGraph Analytics项目社区图谱
Entities:
(wechat: knxy0616#company)
(TuGraph-family/tugraph-analytics#github_repo)
(ANT GROUP#company)
(杭州电子科技大学#company)
(@alibaba#company)
(Ant Group#company)
(China#country)
(United States#country)
(Hong Kong#country)
(Japan#country)
(Taiwan#country)
(fanzhidongyzby#github_user)
(baizn#github_user)
(qingwen220#github_user)
(kaori-seasons#github_user)
(pengzhiwei2018#github_user)
(ramboloc#github_user)

Relationships:
(wechat: knxy0616#PR#TuGraph-family/tugraph-analytics#PR count 1)
(ANT GROUP#PR#TuGraph-family/tugraph-analytics#PR count 9)
(杭州电子科技大学#PR#TuGraph-family/tugraph-analytics#PR count 2)
(@alibaba#PR#TuGraph-family/tugraph-analytics#PR count 14)
(Ant Group#PR#TuGraph-family/tugraph-analytics#PR count 19)
(China#Star#TuGraph-family/tugraph-analytics#Star count 159)
(United States#Star#TuGraph-family/tugraph-analytics#Star count 11)
(Hong Kong#Star#TuGraph-family/tugraph-analytics#Star count 3)
(Japan#Star#TuGraph-family/tugraph-analytics#Star count 2)
(Taiwan#Star#TuGraph-family/tugraph-analytics#Star count 2)
(fanzhidongyzby#belong_to#China#belong_to)
(baizn#belong_to#China#belong_to)
(qingwen220#belong_to#China#belong_to)
(kaori-seasons#belong_to#China#belong_to)
(pengzhiwei2018#belong_to#China#belong_to)
(ramboloc#belong_to#China#belong_to)
(baizn#belong_to#ANT GROUP#belong_to)
(kaori-seasons#belong_to#wechat: knxy0616#belong_to)
(pengzhiwei2018#belong_to#Ant Group#belong_to)
(ramboloc#belong_to#杭州电子科技大学#belong_to)
(fanzhidongyzby#belong_to#@alibaba#belong_to)
### 3.3 TuGraph Analytics项目贡献图谱
Entities:
(pengzhiwei2018#github_user)
(TuGraph-family/tugraph-analytics#github_repo)
(Leomrlin#github_user)
(652053395#github_user)
(KevinLi724#github_user)
(CLAassistant#github_user)
(fanzhidongyzby#github_user)
(qingfei1994#github_user)
(huang12zheng#github_user)
(cfsfine#github_user)

Relationships:
(pengzhiwei2018#push#TuGraph-family/tugraph-analytics#push count 3)
(Leomrlin#push#TuGraph-family/tugraph-analytics#push count 1)
(652053395#open_pr#TuGraph-family/tugraph-analytics#open_pr count 1)
(Leomrlin#open_pr#TuGraph-family/tugraph-analytics#open_pr count 1)
(KevinLi724#open_pr#TuGraph-family/tugraph-analytics#open_pr count 1)
(CLAassistant#code_review#TuGraph-family/tugraph-analytics#code_review count 2)
(pengzhiwei2018#code_review#TuGraph-family/tugraph-analytics#code_review count 1)
(fanzhidongyzby#code_review#TuGraph-family/tugraph-analytics#code_review count 1)
(qingfei1994#open_issue#TuGraph-family/tugraph-analytics#open_issue count 2)
(huang12zheng#open_issue#TuGraph-family/tugraph-analytics#open_issue count 1)
(KevinLi724#open_issue#TuGraph-family/tugraph-analytics#open_issue count 1)
(qingfei1994#comment_issue#TuGraph-family/tugraph-analytics#comment_issue count 1)
(Leomrlin#comment_issue#TuGraph-family/tugraph-analytics#comment_issue count 1)
(cfsfine#comment_issue#TuGraph-family/tugraph-analytics#comment_issue count 1)
## 4. RocksDB开源图谱
### 4.1 RocksDB项目生态图谱
Entities:
(facebook/rocksdb#github_repo)
(golang/go#github_repo)
(rust-lang/rust#github_repo)
(facebook/folly#github_repo)
(microsoft/vscode#github_repo)
(protocolbuffers/protobuf#github_repo)
(grpc/grpc#github_repo)
(moby/moby#github_repo)
(tensorflow/tensorflow#github_repo)
(redis/redis#github_repo)
(Homebrew/homebrew-core#github_repo)
(Homebrew#github_organization)
(microsoft#github_organization)
(tensorflow#github_organization)
(grpc#github_organization)
(rust-lang#github_organization)
(golang#github_organization)
(docker#github_organization)
(google#github_organization)
(redis#github_organization)
(facebook#github_organization)

Relationships:
(facebook/rocksdb#common_developer#golang/go#common_developer count 166)
(facebook/rocksdb#common_developer#rust-lang/rust#common_developer count 155)
(facebook/rocksdb#common_developer#facebook/folly#common_developer count 112)
(facebook/rocksdb#common_developer#microsoft/vscode#common_developer count 107)
(facebook/rocksdb#common_developer#protocolbuffers/protobuf#common_developer count 107)
(facebook/rocksdb#common_developer#grpc/grpc#common_developer count 106)
(facebook/rocksdb#common_developer#moby/moby#common_developer count 103)
(facebook/rocksdb#common_developer#tensorflow/tensorflow#common_developer count 101)
(facebook/rocksdb#common_developer#redis/redis#common_developer count 97)
(facebook/rocksdb#common_developer#Homebrew/homebrew-core#common_developer count 95)
(Homebrew/homebrew-core#belong_to#Homebrew#belong_to)
(microsoft/vscode#belong_to#microsoft#belong_to)
(tensorflow/tensorflow#belong_to#tensorflow#belong_to)
(grpc/grpc#belong_to#grpc#belong_to)
(rust-lang/rust#belong_to#rust-lang#belong_to)
(golang/go#belong_to#golang#belong_to)
(moby/moby#belong_to#docker#belong_to)
(protocolbuffers/protobuf#belong_to#google#belong_to)
(redis/redis#belong_to#redis#belong_to)
(facebook/rocksdb#belong_to#facebook#belong_to)
(facebook/folly#belong_to#facebook#belong_to)
### 4.2 RocksDB项目社区图谱
Entities:
(@alibaba#company)
(apache/flink#github_repo)
(@apache#company)
(Alibaba#company)
(Tencent#company)
(alibaba#company)
(China#country)
(United States#country)
(Germany#country)
(India#country)
(Brazil#country)
(KKcorps#github_user)
(mghildiy#github_user)
(Samrat002#github_user)
(nycholas#github_user)
(EronWright#github_user)
(hsaputra#github_user)
(syhily#github_user)
(wuchong#github_user)
(zjuwangg#github_user)
(yanghua#github_user)
(Myasuka#github_user)
(kylemeow#github_user)
(JingsongLi#github_user)
(SteNicholas#github_user)
(wangyang0918#github_user)
(lincoln-lil#github_user)
(XuQianJin-Stars#github_user)
(xishuaidelin#github_user)
(mxm#github_user)
(rmetzger#github_user)
(JingGe#github_user)
(hequn8128#github_user)

Relationships:
(@alibaba#PR#apache/flink#PR count 1489)
(@apache#PR#apache/flink#PR count 508)
(Alibaba#PR#apache/flink#PR count 510)
(Tencent#PR#apache/flink#PR count 79)
(alibaba#PR#apache/flink#PR count 201)
(China#Star#apache/flink#Star count 4003)
(United States#Star#apache/flink#Star count 604)
(Germany#Star#apache/flink#Star count 199)
(India#Star#apache/flink#Star count 158)
(Brazil#Star#apache/flink#Star count 113)
(KKcorps#belong_to#India#belong_to)
(mghildiy#belong_to#India#belong_to)
(Samrat002#belong_to#India#belong_to)
(nycholas#belong_to#Brazil#belong_to)
(EronWright#belong_to#United States#belong_to)
(hsaputra#belong_to#United States#belong_to)
(syhily#belong_to#United States#belong_to)
(wuchong#belong_to#China#belong_to)
(zjuwangg#belong_to#China#belong_to)
(yanghua#belong_to#China#belong_to)
(Myasuka#belong_to#China#belong_to)
(kylemeow#belong_to#China#belong_to)
(JingsongLi#belong_to#China#belong_to)
(SteNicholas#belong_to#China#belong_to)
(wangyang0918#belong_to#China#belong_to)
(lincoln-lil#belong_to#China#belong_to)
(XuQianJin-Stars#belong_to#China#belong_to)
(xishuaidelin#belong_to#China#belong_to)
(mxm#belong_to#Germany#belong_to)
(rmetzger#belong_to#Germany#belong_to)
(JingGe#belong_to#Germany#belong_to)
(kylemeow#belong_to#Tencent#belong_to)
(mxm#belong_to#@apache#belong_to)
(wuchong#belong_to#@alibaba#belong_to)
(zjuwangg#belong_to#alibaba#belong_to)
(hequn8128#belong_to#Alibaba#belong_to)
### 4.3 RocksDB项目贡献图谱
Entities:
(facebook/rocksdb#github_repo)
(golang/go#github_repo)
(rust-lang/rust#github_repo)
(facebook/folly#github_repo)
(microsoft/vscode#github_repo)
(protocolbuffers/protobuf#github_repo)
(grpc/grpc#github_repo)
(moby/moby#github_repo)
(tensorflow/tensorflow#github_repo)
(redis/redis#github_repo)
(Homebrew/homebrew-core#github_repo)
(Homebrew#github_organization)
(microsoft#github_organization)
(tensorflow#github_organization)
(grpc#github_organization)
(rust-lang#github_organization)
(golang#github_organization)
(docker#github_organization)
(google#github_organization)
(redis#github_organization)
(facebook#github_organization)

Relationships:
(facebook/rocksdb#common_developer#golang/go#common_developer count 166)
(facebook/rocksdb#common_developer#rust-lang/rust#common_developer count 155)
(facebook/rocksdb#common_developer#facebook/folly#common_developer count 112)
(facebook/rocksdb#common_developer#microsoft/vscode#common_developer count 107)
(facebook/rocksdb#common_developer#protocolbuffers/protobuf#common_developer count 107)
(facebook/rocksdb#common_developer#grpc/grpc#common_developer count 106)
(facebook/rocksdb#common_developer#moby/moby#common_developer count 103)
(facebook/rocksdb#common_developer#tensorflow/tensorflow#common_developer count 101)
(facebook/rocksdb#common_developer#redis/redis#common_developer count 97)
(facebook/rocksdb#common_developer#Homebrew/homebrew-core#common_developer count 95)
(Homebrew/homebrew-core#belong_to#Homebrew#belong_to)
(microsoft/vscode#belong_to#microsoft#belong_to)
(tensorflow/tensorflow#belong_to#tensorflow#belong_to)
(grpc/grpc#belong_to#grpc#belong_to)
(rust-lang/rust#belong_to#rust-lang#belong_to)
(golang/go#belong_to#golang#belong_to)
(moby/moby#belong_to#docker#belong_to)
(protocolbuffers/protobuf#belong_to#google#belong_to)
(redis/redis#belong_to#redis#belong_to)
(facebook/rocksdb#belong_to#facebook#belong_to)
(facebook/folly#belong_to#facebook#belong_to)

## 5. Flink开源图谱
### 5.1 Flink项目生态图谱
Entities:
(apache/flink#github_repo)
(apache/spark#github_repo)
(apache/flink-web#github_repo)
(apache/flink-cdc#github_repo)
(apache/iceberg#github_repo)
(apache/hudi#github_repo)
(apache/kafka#github_repo)
(apache/paimon#github_repo)
(apache/pulsar#github_repo)
(apache/calcite#github_repo)
(apache/hadoop#github_repo)
(apache#github_organization)

Relationships:
(apache/flink#common_developer#apache/spark#common_developer count 223)
(apache/flink#common_developer#apache/flink-web#common_developer count 201)
(apache/flink#common_developer#apache/flink-cdc#common_developer count 173)
(apache/flink#common_developer#apache/iceberg#common_developer count 171)
(apache/flink#common_developer#apache/hudi#common_developer count 153)
(apache/flink#common_developer#apache/kafka#common_developer count 132)
(apache/flink#common_developer#apache/paimon#common_developer count 117)
(apache/flink#common_developer#apache/pulsar#common_developer count 102)
(apache/flink#common_developer#apache/calcite#common_developer count 99)
(apache/flink#common_developer#apache/hadoop#common_developer count 96)
(apache/spark#belong_to#apache#belong_to)
(apache/kafka#belong_to#apache#belong_to)
(apache/hadoop#belong_to#apache#belong_to)
(apache/calcite#belong_to#apache#belong_to)
(apache/flink#belong_to#apache#belong_to)
(apache/flink-web#belong_to#apache#belong_to)
(apache/pulsar#belong_to#apache#belong_to)
(apache/hudi#belong_to#apache#belong_to)
(apache/iceberg#belong_to#apache#belong_to)
(apache/flink-cdc#belong_to#apache#belong_to)
(apache/paimon#belong_to#apache#belong_to)
### 5.2 Flink项目社区图谱
Entities:
(@alibaba#company)
(apache/flink#github_repo)
(@apache#company)
(Alibaba#company)
(Tencent#company)
(alibaba#company)
(China#country)
(United States#country)
(Germany#country)
(India#country)
(Brazil#country)
(KKcorps#github_user)
(mghildiy#github_user)
(Samrat002#github_user)
(nycholas#github_user)
(EronWright#github_user)
(hsaputra#github_user)
(syhily#github_user)
(wuchong#github_user)
(zjuwangg#github_user)
(yanghua#github_user)
(Myasuka#github_user)
(kylemeow#github_user)
(JingsongLi#github_user)
(SteNicholas#github_user)
(wangyang0918#github_user)
(lincoln-lil#github_user)
(XuQianJin-Stars#github_user)
(xishuaidelin#github_user)
(mxm#github_user)
(rmetzger#github_user)
(JingGe#github_user)
(hequn8128#github_user)

Relationships:
(@alibaba#PR#apache/flink#PR count 1489)
(@apache#PR#apache/flink#PR count 508)
(Alibaba#PR#apache/flink#PR count 510)
(Tencent#PR#apache/flink#PR count 79)
(alibaba#PR#apache/flink#PR count 201)
(China#Star#apache/flink#Star count 4003)
(United States#Star#apache/flink#Star count 604)
(Germany#Star#apache/flink#Star count 199)
(India#Star#apache/flink#Star count 158)
(Brazil#Star#apache/flink#Star count 113)
(KKcorps#belong_to#India#belong_to)
(mghildiy#belong_to#India#belong_to)
(Samrat002#belong_to#India#belong_to)
(nycholas#belong_to#Brazil#belong_to)
(EronWright#belong_to#United States#belong_to)
(hsaputra#belong_to#United States#belong_to)
(syhily#belong_to#United States#belong_to)
(wuchong#belong_to#China#belong_to)
(zjuwangg#belong_to#China#belong_to)
(yanghua#belong_to#China#belong_to)
(Myasuka#belong_to#China#belong_to)
(kylemeow#belong_to#China#belong_to)
(JingsongLi#belong_to#China#belong_to)
(SteNicholas#belong_to#China#belong_to)
(wangyang0918#belong_to#China#belong_to)
(lincoln-lil#belong_to#China#belong_to)
(XuQianJin-Stars#belong_to#China#belong_to)
(xishuaidelin#belong_to#China#belong_to)
(mxm#belong_to#Germany#belong_to)
(rmetzger#belong_to#Germany#belong_to)
(JingGe#belong_to#Germany#belong_to)
(kylemeow#belong_to#Tencent#belong_to)
(mxm#belong_to#@apache#belong_to)
(wuchong#belong_to#@alibaba#belong_to)
(zjuwangg#belong_to#alibaba#belong_to)
(hequn8128#belong_to#Alibaba#belong_to)
### 5.3 Flink项目贡献图谱
Entities:
(XComp#github_user)
(apache/flink#github_repo)
(masteryhx#github_user)
(HuangXingBo#github_user)
(Myasuka#github_user)
(1996fanrui#github_user)
(dawidwys#github_user)
(Jiabao-Sun#github_user)
(fredia#github_user)
(zentol#github_user)
(LadyForest#github_user)
(liuyongvs#github_user)
(snuyanzin#github_user)
(lincoln-lil#github_user)
(jeyhunkarimov#github_user)
(Zakelly#github_user)
(JunRuiLee#github_user)
(flinkbot#github_user)
(WencongLiu#github_user)
(morazow#github_user)
(spoon-lz#github_user)

Relationships:
(XComp#push#apache/flink#push count 19)
(masteryhx#push#apache/flink#push count 15)
(HuangXingBo#push#apache/flink#push count 10)
(Myasuka#push#apache/flink#push count 8)
(1996fanrui#push#apache/flink#push count 7)
(dawidwys#push#apache/flink#push count 7)
(Jiabao-Sun#push#apache/flink#push count 6)
(fredia#push#apache/flink#push count 6)
(zentol#push#apache/flink#push count 5)
(LadyForest#push#apache/flink#push count 5)
(liuyongvs#open_pr#apache/flink#open_pr count 21)
(XComp#open_pr#apache/flink#open_pr count 18)
(snuyanzin#open_pr#apache/flink#open_pr count 15)
(lincoln-lil#open_pr#apache/flink#open_pr count 8)
(masteryhx#open_pr#apache/flink#open_pr count 7)
(jeyhunkarimov#open_pr#apache/flink#open_pr count 6)
(Zakelly#open_pr#apache/flink#open_pr count 5)
(JunRuiLee#open_pr#apache/flink#open_pr count 4)
(fredia#open_pr#apache/flink#open_pr count 4)
(Jiabao-Sun#open_pr#apache/flink#open_pr count 4)
(flinkbot#code_review#apache/flink#code_review count 160)
(XComp#code_review#apache/flink#code_review count 142)
(snuyanzin#code_review#apache/flink#code_review count 87)
(Zakelly#code_review#apache/flink#code_review count 84)
(WencongLiu#code_review#apache/flink#code_review count 73)
(masteryhx#code_review#apache/flink#code_review count 72)
(LadyForest#code_review#apache/flink#code_review count 64)
(morazow#code_review#apache/flink#code_review count 63)
(liuyongvs#code_review#apache/flink#code_review count 51)
(spoon-lz#code_review#apache/flink#code_review count 44)

## 6. Spark开源图谱
### 6.1 Spark项目生态图谱
Entities:
(apache/spark#github_repo)
(delta-io/delta#github_repo)
(apache/flink#github_repo)
(tensorflow/tensorflow#github_repo)
(apache/iceberg#github_repo)
(apache/hadoop#github_repo)
(apache/airflow#github_repo)
(kubernetes/kubernetes#github_repo)
(apache/arrow#github_repo)
(trinodb/trino#github_repo)
(apache/kafka#github_repo)
(trinodb#github_organization)
(delta-io#github_organization)
(tensorflow#github_organization)
(apache#github_organization)
(GoogleCloudPlatform#github_organization)

Relationships:
(apache/spark#common_developer#delta-io/delta#common_developer count 219)
(apache/spark#common_developer#apache/flink#common_developer count 209)
(apache/spark#common_developer#tensorflow/tensorflow#common_developer count 206)
(apache/spark#common_developer#apache/iceberg#common_developer count 199)
(apache/spark#common_developer#apache/hadoop#common_developer count 180)
(apache/spark#common_developer#apache/airflow#common_developer count 175)
(apache/spark#common_developer#kubernetes/kubernetes#common_developer count 167)
(apache/spark#common_developer#apache/arrow#common_developer count 153)
(apache/spark#common_developer#trinodb/trino#common_developer count 140)
(apache/spark#common_developer#apache/kafka#common_developer count 137)
(trinodb/trino#belong_to#trinodb#belong_to)
(delta-io/delta#belong_to#delta-io#belong_to)
(tensorflow/tensorflow#belong_to#tensorflow#belong_to)
(apache/spark#belong_to#apache#belong_to)
(apache/kafka#belong_to#apache#belong_to)
(apache/hadoop#belong_to#apache#belong_to)
(apache/flink#belong_to#apache#belong_to)
(apache/airflow#belong_to#apache#belong_to)
(apache/arrow#belong_to#apache#belong_to)
(apache/iceberg#belong_to#apache#belong_to)
(kubernetes/kubernetes#belong_to#GoogleCloudPlatform#belong_to)
### 6.2 Spark项目社区图谱
Entities:
(@databricks#company)
(apache/spark#github_repo)
(Tencent#company)
(Databricks#company)
(Microsoft#company)
(Amazon Web Services#company)
(China#country)
(United States#country)
(India#country)
(Germany#country)
(Brazil#country)
(prabeesh#github_user)
(krishnakalyan3#github_user)
(rshkv#github_user)
(kul#github_user)
(shardulm94#github_user)
(priyansh19#github_user)
(rimolive#github_user)
(LeoColman#github_user)
(lucaspompeun#github_user)
(rxin#github_user)
(JoshRosen#github_user)
(viirya#github_user)
(liancheng#github_user)
(brandondahler#github_user)
(dongjoon-hyun#github_user)
(cfregly#github_user)
(apivovarov#github_user)
(yeshengm#github_user)
(imatiach-msft#github_user)
(zhengruifeng#github_user)
(Eric5553#github_user)
(LuciferYang#github_user)
(sharkdtu#github_user)
(yaooqinn#github_user)
(iemejia#github_user)

Relationships:
(@databricks#PR#apache/spark#PR count 562)
(Tencent#PR#apache/spark#PR count 46)
(Databricks#PR#apache/spark#PR count 2521)
(Microsoft#PR#apache/spark#PR count 54)
(Amazon Web Services#PR#apache/spark#PR count 12)
(China#Star#apache/spark#Star count 3856)
(United States#Star#apache/spark#Star count 1431)
(India#Star#apache/spark#Star count 321)
(Germany#Star#apache/spark#Star count 282)
(Brazil#Star#apache/spark#Star count 241)
(prabeesh#belong_to#Germany#belong_to)
(krishnakalyan3#belong_to#Germany#belong_to)
(rshkv#belong_to#Germany#belong_to)
(kul#belong_to#India#belong_to)
(shardulm94#belong_to#India#belong_to)
(priyansh19#belong_to#India#belong_to)
(rimolive#belong_to#Brazil#belong_to)
(LeoColman#belong_to#Brazil#belong_to)
(lucaspompeun#belong_to#Brazil#belong_to)
(rxin#belong_to#United States#belong_to)
(JoshRosen#belong_to#United States#belong_to)
(viirya#belong_to#United States#belong_to)
(liancheng#belong_to#United States#belong_to)
(brandondahler#belong_to#United States#belong_to)
(dongjoon-hyun#belong_to#United States#belong_to)
(cfregly#belong_to#United States#belong_to)
(apivovarov#belong_to#United States#belong_to)
(yeshengm#belong_to#United States#belong_to)
(imatiach-msft#belong_to#United States#belong_to)
(zhengruifeng#belong_to#China#belong_to)
(Eric5553#belong_to#China#belong_to)
(LuciferYang#belong_to#China#belong_to)
(sharkdtu#belong_to#China#belong_to)
(yaooqinn#belong_to#China#belong_to)
(brandondahler#belong_to#Amazon Web Services#belong_to)
(Eric5553#belong_to#Tencent#belong_to)
(iemejia#belong_to#Microsoft#belong_to)
(rxin#belong_to#Databricks#belong_to)
(liancheng#belong_to#@databricks#belong_to)
### 6.3 Spark项目贡献图谱
Entities:
(dongjoon-hyun#github_user)
(apache/spark#github_repo)
(HyukjinKwon#github_user)
(MaxGekk#github_user)
(yaooqinn#github_user)
(cloud-fan#github_user)
(HeartSaVioR#github_user)
(gengliangwang#github_user)
(zhengruifeng#github_user)
(LuciferYang#github_user)
(ueshin#github_user)
(panbingkun#github_user)
(stefankandic#github_user)
(nchammas#github_user)
(dbatomic#github_user)
(anishshri-db#github_user)
(mihailom-db#github_user)

Relationships:
(dongjoon-hyun#push#apache/spark#push count 124)
(HyukjinKwon#push#apache/spark#push count 78)
(MaxGekk#push#apache/spark#push count 41)
(yaooqinn#push#apache/spark#push count 38)
(cloud-fan#push#apache/spark#push count 35)
(HeartSaVioR#push#apache/spark#push count 15)
(gengliangwang#push#apache/spark#push count 11)
(zhengruifeng#push#apache/spark#push count 11)
(LuciferYang#push#apache/spark#push count 8)
(ueshin#push#apache/spark#push count 4)
(dongjoon-hyun#open_pr#apache/spark#open_pr count 44)
(panbingkun#open_pr#apache/spark#open_pr count 39)
(HyukjinKwon#open_pr#apache/spark#open_pr count 30)
(yaooqinn#open_pr#apache/spark#open_pr count 28)
(zhengruifeng#open_pr#apache/spark#open_pr count 26)
(LuciferYang#open_pr#apache/spark#open_pr count 14)
(stefankandic#open_pr#apache/spark#open_pr count 9)
(gengliangwang#open_pr#apache/spark#open_pr count 8)
(cloud-fan#open_pr#apache/spark#open_pr count 8)
(nchammas#open_pr#apache/spark#open_pr count 7)
(dongjoon-hyun#code_review#apache/spark#code_review count 805)
(cloud-fan#code_review#apache/spark#code_review count 731)
(HyukjinKwon#code_review#apache/spark#code_review count 453)
(MaxGekk#code_review#apache/spark#code_review count 302)
(dbatomic#code_review#apache/spark#code_review count 247)
(yaooqinn#code_review#apache/spark#code_review count 218)
(anishshri-db#code_review#apache/spark#code_review count 199)
(mihailom-db#code_review#apache/spark#code_review count 147)
(panbingkun#code_review#apache/spark#code_review count 118)
(HeartSaVioR#code_review#apache/spark#code_review count 115)
## 7. 个人开源图谱
### 7.1 开发活动图谱
Entities:
(siying#github_user)
(siying/rocksdb#github_repo)
(facebook/rocksdb#github_repo)
(siying/spark#github_repo)
(siying/siying.github.io#github_repo)
(siying/china-indie-podcasts#github_repo)
(AGSaidi/rocksdb#github_repo)
(darionyaphet/rocksdb#github_repo)
(PraveenSinghRao/rocksdb#github_repo)
(siying/mysql-5.6#github_repo)
(lhsoft/rocksdb#github_repo)
(apache/spark#github_repo)
(avocadotoastlive/avocadotoast.live#github_repo)
(typlog/china-indie-podcasts#github_repo)
(facebook/mysql-5.6#github_repo)
(Homebrew/legacy-homebrew#github_repo)
(google/leveldb#github_repo)
(MySQLOnRocksDB/mysql-5.6#github_repo)
(facebook/zstd#github_repo)
(facebook/react#github_repo)

Relationships:
(siying#push#siying/rocksdb#push count 1376)
(siying#push#facebook/rocksdb#push count 805)
(siying#push#siying/spark#push count 79)
(siying#push#siying/siying.github.io#push count 5)
(siying#push#siying/china-indie-podcasts#push count 3)
(siying#push#AGSaidi/rocksdb#push count 2)
(siying#push#darionyaphet/rocksdb#push count 2)
(siying#push#PraveenSinghRao/rocksdb#push count 2)
(siying#push#siying/mysql-5.6#push count 2)
(siying#push#lhsoft/rocksdb#push count 1)
(siying#open_pr#facebook/rocksdb#open_pr count 614)
(siying#open_pr#apache/spark#open_pr count 19)
(siying#open_pr#avocadotoastlive/avocadotoast.live#open_pr count 2)
(siying#open_pr#siying/rocksdb#open_pr count 1)
(siying#open_pr#typlog/china-indie-podcasts#open_pr count 1)
(siying#open_pr#facebook/mysql-5.6#open_pr count 1)
(siying#code_review#facebook/rocksdb#code_review count 4189)
(siying#code_review#apache/spark#code_review count 82)
(siying#code_review#avocadotoastlive/avocadotoast.live#code_review count 4)
(siying#code_review#facebook/mysql-5.6#code_review count 2)
(siying#code_review#Homebrew/legacy-homebrew#code_review count 1)
(siying#code_review#google/leveldb#code_review count 1)
(siying#open_issue#facebook/rocksdb#open_issue count 14)
(siying#open_issue#avocadotoastlive/avocadotoast.live#open_issue count 3)
(siying#comment_issue#facebook/rocksdb#comment_issue count 718)
(siying#comment_issue#facebook/mysql-5.6#comment_issue count 10)
(siying#comment_issue#MySQLOnRocksDB/mysql-5.6#comment_issue count 8)
(siying#comment_issue#facebook/zstd#comment_issue count 1)
(siying#comment_issue#facebook/react#comment_issue count 1)
### 7.2 开源伙伴图谱
Entities:
(siying#github_user)
(gfosco#github_user)
(adamretter#github_user)
(mdcallag#github_user)
(ajkr#github_user)
(igorcanadi#github_user)
(yuslepukhin#github_user)
(yiwu-arbug#github_user)
(dhruba#github_user)
(maysamyabandeh#github_user)
(sagar0#github_user)
(facebook-github-bot#github_user)
(riversand963#github_user)
(pdillinger#github_user)
(anand1976#github_user)
(IslamAbdelRahman#github_user)
(chaitanya9186#github_user)
(Vincenthays#github_user)
(work-mohit#github_user)
(PhilYue#github_user)
(liangxj8#github_user)
(ew0s#github_user)
(raubitsj#github_user)
(nchilaka#github_user)
(smifun#github_user)
(cellogx#github_user)
(lshmouse#github_user)
(rockeet#github_user)
(cclauss#github_user)
(baotiao#github_user)
(chfast#github_user)
(spetrunia#github_user)
(inikep#github_user)
(elfring#github_user)

Relationships:
(siying#common_issue#gfosco#common_issue count 95)
(siying#common_issue#adamretter#common_issue count 54)
(siying#common_issue#mdcallag#common_issue count 44)
(siying#common_issue#ajkr#common_issue count 42)
(siying#common_issue#igorcanadi#common_issue count 37)
(siying#common_issue#yuslepukhin#common_issue count 30)
(siying#common_issue#yiwu-arbug#common_issue count 26)
(siying#common_issue#dhruba#common_issue count 23)
(siying#common_issue#maysamyabandeh#common_issue count 19)
(siying#common_issue#sagar0#common_issue count 19)
(siying#common_pr#facebook-github-bot#common_pr count 1316)
(siying#common_pr#ajkr#common_pr count 266)
(siying#common_pr#riversand963#common_pr count 185)
(siying#common_pr#pdillinger#common_pr count 137)
(siying#common_pr#yiwu-arbug#common_pr count 128)
(siying#common_pr#maysamyabandeh#common_pr count 117)
(siying#common_pr#sagar0#common_pr count 102)
(siying#common_pr#anand1976#common_pr count 99)
(siying#common_pr#IslamAbdelRahman#common_pr count 76)
(siying#common_pr#yuslepukhin#common_pr count 73)
(siying#common_star#chaitanya9186#common_star count 1)
(siying#common_star#Vincenthays#common_star count 1)
(siying#common_star#work-mohit#common_star count 1)
(siying#common_star#PhilYue#common_star count 1)
(siying#common_star#liangxj8#common_star count 1)
(siying#common_star#ew0s#common_star count 1)
(siying#common_star#raubitsj#common_star count 1)
(siying#common_star#nchilaka#common_star count 1)
(siying#common_star#smifun#common_star count 1)
(siying#common_star#cellogx#common_star count 1)
(siying#common_repo#facebook-github-bot#common_repo count 4)
(siying#common_repo#mdcallag#common_repo count 4)
(siying#common_repo#lshmouse#common_repo count 4)
(siying#common_repo#rockeet#common_repo count 3)
(siying#common_repo#cclauss#common_repo count 3)
(siying#common_repo#baotiao#common_repo count 3)
(siying#common_repo#chfast#common_repo count 3)
(siying#common_repo#spetrunia#common_repo count 3)
(siying#common_repo#inikep#common_repo count 3)
(siying#common_repo#elfring#common_repo count 3)
### 7.3 开源兴趣图谱
Entities:
(dongjoon-hyun#github_user)
(java#topic)
(scala#topic)
(python#topic)
(apache#topic)
(big-data#topic)
(apache/spark#github_repo)
(apache/orc#github_repo)
(apache/spark-kubernetes-operator#github_repo)

Relationships:
(dongjoon-hyun#repo#java#repo count 17)
(dongjoon-hyun#repo#scala#repo count 9)
(dongjoon-hyun#repo#python#repo count 9)
(dongjoon-hyun#repo#apache#repo count 8)
(dongjoon-hyun#repo#big-data#repo count 8)
(apache/spark#belong_to#java#belong_to)
(apache/spark#belong_to#scala#belong_to)
(apache/spark#belong_to#python#belong_to)
(apache/spark#belong_to#big-data#belong_to)
(apache/orc#belong_to#java#belong_to)
(apache/orc#belong_to#apache#belong_to)
(apache/orc#belong_to#big-data#belong_to)
(apache/spark-kubernetes-operator#belong_to#java#belong_to)

## 8. 图计算论文图谱
### 8.1 论文图谱子图-1
Entities:
(A1#A Distributed In-Memory Graph Database)
(Trinity#A Distributed Graph Engine on a Memory Cloud)
(ASPIRE#Exploiting Asynchronous Parallelism in Iterative Algorithms using a Relaxed Consistency based DSM)
(Ligra#A Lightweight Graph Processing Framework for Shared Memory)
(AeonG#An Efficient Built-in Temporal Support in Graph Databases (Extended Version)
(Auxo#A Temporal Graph Management System)
(G-Tran#A High Performance Distributed Graph Database with a Decentralized Architecture)
(Gradoop#Gradoop)
(Graphflow#An Active Graph Database)
(LiveGraph#A Transactional Graph Sdstrage System with Purely Sequential Adjacency List Scans)
(AsynGraph#Maximizing Data Parallelism for Efficient Iterative Graph Processing on GPUs)
(DiGraph#An Efficient Path-based Iterative Directed Graph Processing System on Multiple GPUs)
(Garaph#Efficient GPU-accelerated Graph Processing on a Single Machine with Balanced Replication)
(Maiter#An Asynchronous Graph Processing Framework for Delta-based Accumulative Iterative Computation)
(Tigr#Transforming Irregular Graphs for GPU-Friendly Graph Processing)
(Chronos#A Graph Engine for Temporal Graph Analysis)
(BG3#A Cost Effective and I/O Efficient Graph Database in ByteDance)
(ByteGraph#A High-Performance Distributed Graph Database in ByteDance)
(Blaze#Fast Graph Processing on Fast SSDs)
(Lumos#Dependency-Driven Disk-based Graph Processing)
(BlitzG#Exploiting high-bandwidth networks for fast graph processing)
(DD-Graph#A Highly Cost-Effective Distributed Disk-based Graph-Processing Framework)
(GRAM#Scaling Graph Computation dst the Trillions)
(GoFFish#A Sub-graph Centric Framework for Large-Scale Graph Analytics)
(MOCgraph#Scalable Distributed Graph Processing Using Message Online Computing)
(Seraph#an Efficient, Low-cost System for Concurrent Graph Processing)
(Blogel#A Block-Centric Framework for Distributed Computation on Real-World Graphs)
(Giraph#DB)
(PowerGraph#Distributed Graph-Parallel Computation on Natural Graphs)
(CGgraph#An Ultra-fast Graph Processing System on Modern Commodity CPU-GPU Co-processor)
(GPOP#A scalable cache- and memory-efficient framework for Graph Processing Over Partitions)
(HyTGraph#GPU-Accelerated Graph Processing with Hybrid Transfer Management)
(LargeGraph#An Efficient Dependency-Aware GPU-Accelerated Large-Scale Graph Processing)
(CGraph#A correlations-aware approach for efficient concurrent iterative graph processing)
(HUS-Graph#I/O-Efficient Out-of-Core Graph Processing with Hybrid Update Strategy)
(MOSAIC#Processing a Trillion-Edge Graph on a Single Machine)
(NXgraph#An Efficient Graph Processing System on a Single Machine*)
(Version Traveler#Fast and Memory-Efficient Version Switching in Graph Processing Systems)
(CSMqGraph#Coarse-Grained and Multi-external-sdstrage Multi-queue I/O Management for Graph Computing)
(Graphene#Fine-Grained IO Management for Graph Computing)
(HGraph#I/O-efficient Distributed and Iterative Graph Computing by Hybrid Pushing/Pulling)
(Chaos#Scale-out Graph Processing src Secondary Sdstrage)
(GridGraph#Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning)
(Chronograph#A Distributed Processing Platform for Online and Batch Computations on Event-sourced Graphs)
(ImmortalGraph#A System for Sdstrage and Analysis of Temporal Graphs)
(Naiad#A Timely Dataflow System)
(CLIP#A Disk I/O Focused Parallel Out-of-core Graph Processing System)
(CoRAL#Confined Recovery in Distributed Asynchronous Graph Processing)
(CommonGraph#Graph Analytics on Evolving Data)
(GraphOne#A Data Sdstre for Real-time Analytics on Evolving Graphs)
(JetStream#Graph Analytics on Streaming Data with Event-Driven Hardware Acceleradstr)
(TDGraph#A dstpology-Driven Acceleradstr for High-Performance Streaming Graph Processing)
(Tripoline#Generalized Incremental Graph Processing via Graph Triangle Inequality)
(CuSha#Vertex-Centric Graph Processing on GPUs)
(Medusa#Simplified Graph Processing on GPUs)
(Cymbalo#An Efficient Graph Processing Framework for Machine Learning)
(GraphD#Distributed Vertex-Centric Graph Processing Beyond the Memory Limit)
(Wonderland#A Novel Abstraction-Based Out-Of-Core Graph Processing System)
(D2Graph#An Efficient and Unified Out-of-Core Graph Computing Model)
(GraphMP(2)#I/O-Efficient Big Graph Analytics on a Single Commodity Machine)
(MMap#Fast Billion-Scale Graph Computation on a PC via Memory Mapping)
(WolfGraph#the Edge-Centric graph processing on GPU)
(DFOGraph#An I/O- and Communication-Efficient System for Distributed Fully-out-of-Core Graph Processing)

Relationships:
(A1#引用#Trinity#论文A1引用论文Trinity)
(ASPIRE#引用#Ligra#论文ASPIRE引用论文Ligra)
(AeonG#引用#Auxo#论文AeonG引用论文Auxo)
(AeonG#引用#G-Tran#论文AeonG引用论文G-Tran)
(AeonG#引用#Gradoop#论文AeonG引用论文Gradoop)
(AeonG#引用#Graphflow#论文AeonG引用论文Graphflow)
(AeonG#引用#LiveGraph#论文AeonG引用论文LiveGraph)
(Auxo#引用#Chronos#论文Auxo引用论文Chronos)
(AsynGraph#引用#DiGraph#论文AsynGraph引用论文DiGraph)
(AsynGraph#引用#Garaph#论文AsynGraph引用论文Garaph)
(AsynGraph#引用#Maiter#论文AsynGraph引用论文Maiter)
(AsynGraph#引用#Tigr#论文AsynGraph引用论文Tigr)
(Chronos#引用#Ligra#论文Chronos引用论文Ligra)
(Chronos#引用#Naiad#论文Chronos引用论文Naiad)
(BG3#引用#ByteGraph#论文BG3引用论文ByteGraph)
(ByteGraph#引用#G-Tran#论文ByteGraph引用论文G-Tran)
(ByteGraph#引用#Graphflow#论文ByteGraph引用论文Graphflow)
(ByteGraph#引用#LiveGraph#论文ByteGraph引用论文LiveGraph)
(Blaze#引用#Lumos#论文Blaze引用论文Lumos)
(BlitzG#引用#DD-Graph#论文BlitzG引用论文DD-Graph)
(BlitzG#引用#GRAM#论文BlitzG引用论文GRAM)
(BlitzG#引用#GoFFish#论文BlitzG引用论文GoFFish)
(BlitzG#引用#MOCgraph#论文BlitzG引用论文MOCgraph)
(BlitzG#引用#Seraph#论文BlitzG引用论文Seraph)
(DD-Graph#引用#Chaos#论文DD-Graph引用论文Chaos)
(Blogel#引用#Giraph#论文Blogel引用数据库Giraph)
(Blogel#引用#PowerGraph#论文Blogel引用论文PowerGraph)
(CGgraph#引用#GPOP#论文CGgraph引用论文GPOP)
(CGgraph#引用#HyTGraph#论文CGgraph引用论文HyTGraph)
(CGgraph#引用#LargeGraph#论文CGgraph引用论文LargeGraph)
(CGraph#引用#GRAM#论文CGraph引用论文GRAM)
(CGraph#引用#HUS-Graph#论文CGraph引用论文HUS-Graph)
(CGraph#引用#MOSAIC#论文CGraph引用论文MOSAIC)
(CGraph#引用#NXgraph#论文CGraph引用论文NXgraph)
(CGraph#引用#Seraph#论文CGraph引用论文Seraph)
(CGraph#引用#Version Traveler#论文CGraph引用论文Version Traveler)
(CSMqGraph#引用#Graphene#论文CSMqGraph引用论文Graphene)
(CSMqGraph#引用#HGraph#论文CSMqGraph引用论文HGraph)
(CSMqGraph#引用#NXgraph#论文CSMqGraph引用论文NXgraph)
(Chaos#引用#GRAM#论文Chaos引用论文GRAM)
(Chaos#引用#GridGraph#论文Chaos引用论文GridGraph)
(Chronograph#引用#ImmortalGraph#论文Chronograph引用论文ImmortalGraph)
(CLIP#引用#Graphene#论文CLIP引用论文Graphene)
(CoRAL#引用#ASPIRE#论文CoRAL引用论文ASPIRE)
(CoRAL#引用#Trinity#论文CoRAL引用论文Trinity)
(CommonGraph#引用#GraphOne#论文CommonGraph引用论文GraphOne)
(CommonGraph#引用#JetStream#论文CommonGraph引用论文JetStream)
(CommonGraph#引用#TDGraph#论文CommonGraph引用论文TDGraph)
(CommonGraph#引用#Tripoline#论文CommonGraph引用论文Tripoline)
(CuSha#引用#Medusa#论文CuSha引用论文Medusa)
(Cymbalo#引用#GraphD#论文Cymbalo引用论文GraphD)
(Cymbalo#引用#Wonderland#论文Cymbalo引用论文Wonderland)
(D2Graph#引用#GraphMP(2)#论文D2Graph引用论文GraphMP(2)
(D2Graph#引用#Graphene#论文D2Graph引用论文Graphene)
(D2Graph#引用#MMap#论文D2Graph引用论文MMap)
(D2Graph#引用#WolfGraph#论文D2Graph引用论文WolfGraph)
(DFOGraph#引用#HGraph#论文DFOGraph引用论文HGraph)

### 8.2 论文图谱子图-2
Entities:
(DFOGraph#An I/O- and Communication-Efficient System for Distributed Fully-out-of-Core Graph Processing)
(HybridGraph#Hybrid Pulling:Pushing for I:O-Efficient Distributed and Iterative Graph Computing)
(Lumos#Dependency-Driven Disk-based Graph Processing)
(DZiG#Sparsity-Aware Incremental Processing of Streaming Graphs)
(GraphOne#A Data Sdstre for Real-time Analytics on Evolving Graphs)
(ImmortalGraph#A System for Sdstrage and Analysis of Temporal Graphs)
(DepGraph#A Dependency-Driven Acceleradstr for Efficient Iterative Graph Processing)
(AsynGraph#Maximizing Data Parallelism for Efficient Iterative Graph Processing on GPUs)
(GGraph#An Efficient Structure-Aware Approach for Iterative Graph Processing)
(GraphABCD#Scaling Out Graph Analytics with Asynchronous Block Coordinate Descent)
(GraphM#An Efficient Sdstrage System for High Throughput of Concurrent Graph Processing)
(GraphPIM#Enabling Instruction-Level PIM Offloading in Graph Computing Frameworks)
(GraphR#Accelerating Graph Processing Using ReRAM)
(NGraph#Parallel Graph Processing in Hybrid Memory Systems)
(DiGraph#An Efficient Path-based Iterative Directed Graph Processing System on Multiple GPUs)
(FBSGraph#Accelerating Asynchronous Graph Processing via Forward and Backward Sweeping)
(GraphGrind#addressing load imbalance of graph partitioning)
(Graphie#Large-Scale Asynchronous Graph Traversals on Just a GPU)
(HotGraph#Efficient Asynchronous Processing for Real-World Graphs)
(NXgraph#An Efficient Graph Processing System on a Single Machine*)
(ReGraph#A Graph Processing Framework that Alternately Shrinks and Repartitions the Graph)
(Wonderland#A Novel Abstraction-Based Out-Of-Core Graph Processing System)
(Differential dataflow#Differential dataflow)
(Pregel#A System for Large-Scale Graph Processing)
(iMapReduce#A Distributed Computing Framework for Iterative Computation)
(Distributed GraphLab#A Framework for Machine Learning and Data Mining in the Cloud)
(PrIter#A Distributed Framework for Prioritized Iterative Computations)
(DiterGraph#dstward I/O-Efficient Incremental Computation over Large Graphs with Billion Edges)
(DynamoGraph#A Distributed System for Large-scale, Temporal Graph Processing, its Implementation and First Observations)
(EGraph#Efficient concurrent GPU-based dynamic graph processing)
(GraphTinker#A High Performance Data Structure for Dynamic Graph Processing)
(EPGraph#An Efficient Graph Computing Model in Persistent Memory System)
(D2Graph#An Efficient and Unified Out-of-Core Graph Computing Model)
(HyVE#Hybrid Vertex-Edge Memory Hierarchy for Energy-Efficient Graph Processing)
(EmptyHeaded#A Relational Engine for Graph Processing)
(GraphX#A Resilient Distributed Graph System on Spark)
(Ligra#A Lightweight Graph Processing Framework for Shared Memory)
(CoRAL#Confined Recovery in Distributed Asynchronous Graph Processing)
(PowerLyra#Differentiated Graph Computation and Partitioning on Skewed Graphs)
(Pregelix#Big(ger) Graph Analytics on A Dataflow Engine)
(Fargraph+#Excavating the parallelism of graph processing workload on RDMA-based far memory system)
(GraphCP#An I/O-Efficient Concurrent Graph Processing Framework)
(HUS-Graph#I/O-Efficient Out-of-Core Graph Processing with Hybrid Update Strategy)
(FENNEL#Streaming Graph Partitioning for Massive Scale Graphs)
(PowerGraph#Distributed Graph-Parallel Computation on Natural Graphs)
(FOG#A Fast Out-of-Core Graph Processing Framework)
(FlashGraph#Processing Billion-Node Graphs on an Array of Commodity SSDs)
(GridGraph#Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning)
(Maiter#An Asynchronous Graph Processing Framework for Delta-based Accumulative Iterative Computation)
(TurboGraph#A Fast Parallel Graph Engine Handling Billion-scale Graphs in a Single PC)
(ForeGraph#Exploring Large-scale Graph Processing on Multi-FPGA Architecture)
(Gemini#A Computation-Centric Distributed Graph Processing System)
(GraVF#A Vertex-Centric Distributed Graph Processing Framework on FPGAs)
(GraphGen#An FPGA Framework for Vertex-Centric Graph Computation)
(Frog#Asynchronous graph processing on GPU with hybrid coloring model)
(CuSha#Vertex-Centric Graph Processing on GPUs)
(X-Stream#Edge-centric Graph Processing using Streaming Partitions)
(G-Sdstre#High-Performance Graph Sdstre for Trillion-Edge Processing)
(Chaos#Scale-out Graph Processing src Secondary Sdstrage)
(G-Tran#A High Performance Distributed Graph Database with a Decentralized Architecture)
(A1#A Distributed In-Memory Graph Database)
(SGraph#A Distributed Streaming System for Processing Big Graphs)
(TigerGraph#DB)
(Weaver#A High-Performance, Transactional Graph Database Based on Refinable Timestamps)
(GBASE#A Scalable and General Graph Management System)
(GFlink#An In-Memory Computing Architecture on Heterogeneous CPU-GPU Clusters for Big Data)
(Medusa#Simplified Graph Processing on GPUs)

Relationships:
(DFOGraph#引用#HybridGraph#论文DFOGraph引用论文HybridGraph)
(DFOGraph#引用#Lumos#论文DFOGraph引用论文Lumos)
(DZiG#引用#GraphOne#论文DZiG引用论文GraphOne)
(DZiG#引用#ImmortalGraph#论文DZiG引用论文ImmortalGraph)
(DZiG#引用#Lumos#论文DZiG引用论文Lumos)
(DepGraph#引用#AsynGraph#论文DepGraph引用论文AsynGraph)
(DepGraph#引用#GGraph#论文DepGraph引用论文GGraph)
(DepGraph#引用#GraphABCD#论文DepGraph引用论文GraphABCD)
(DepGraph#引用#GraphM#论文DepGraph引用论文GraphM)
(DepGraph#引用#GraphPIM#论文DepGraph引用论文GraphPIM)
(DepGraph#引用#GraphR#论文DepGraph引用论文GraphR)
(DepGraph#引用#NGraph#论文DepGraph引用论文NGraph)
(DiGraph#引用#FBSGraph#论文DiGraph引用论文FBSGraph)
(DiGraph#引用#GraphGrind#论文DiGraph引用论文GraphGrind)
(DiGraph#引用#Graphie#论文DiGraph引用论文Graphie)
(DiGraph#引用#HotGraph#论文DiGraph引用论文HotGraph)
(DiGraph#引用#NXgraph#论文DiGraph引用论文NXgraph)
(DiGraph#引用#ReGraph#论文DiGraph引用论文ReGraph)
(DiGraph#引用#Wonderland#论文DiGraph引用论文Wonderland)
(FBSGraph#引用#CoRAL#论文FBSGraph引用论文CoRAL)
(FBSGraph#引用#PowerLyra#论文FBSGraph引用论文PowerLyra)
(FBSGraph#引用#Pregelix#论文FBSGraph引用论文Pregelix)
(Differential dataflow#引用#Pregel#论文Differential dataflow引用论文Pregel)
(Differential dataflow#引用#iMapReduce#论文Differential dataflow引用论文iMapReduce)
(Distributed GraphLab#引用#PrIter#论文Distributed GraphLab引用论文PrIter)
(DiterGraph#引用#HybridGraph#论文DiterGraph引用论文HybridGraph)
(DynamoGraph#引用#ImmortalGraph#论文DynamoGraph引用论文ImmortalGraph)
(EGraph#引用#DZiG#论文EGraph引用论文DZiG)
(EGraph#引用#DiGraph#论文EGraph引用论文DiGraph)
(EGraph#引用#GraphTinker#论文EGraph引用论文GraphTinker)
(EPGraph#引用#D2Graph#论文EPGraph引用论文D2Graph)
(EPGraph#引用#DFOGraph#论文EPGraph引用论文DFOGraph)
(EPGraph#引用#HyVE#论文EPGraph引用论文HyVE)
(EmptyHeaded#引用#GraphX#论文EmptyHeaded引用论文GraphX)
(EmptyHeaded#引用#Ligra#论文EmptyHeaded引用论文Ligra)
(Fargraph+#引用#GraphCP#论文Fargraph+引用论文GraphCP)
(Fargraph+#引用#HUS-Graph#论文Fargraph+引用论文HUS-Graph)
(FENNEL#引用#PowerGraph#论文FENNEL引用论文PowerGraph)
(FOG#引用#FlashGraph#论文FOG引用论文FlashGraph)
(FOG#引用#GridGraph#论文FOG引用论文GridGraph)
(FlashGraph#引用#Maiter#论文FlashGraph引用论文Maiter)
(FlashGraph#引用#TurboGraph#论文FlashGraph引用论文TurboGraph)
(ForeGraph#引用#Gemini#论文ForeGraph引用论文Gemini)
(ForeGraph#引用#GraVF#论文ForeGraph引用论文GraVF)
(ForeGraph#引用#GraphGen#论文ForeGraph引用论文GraphGen)
(ForeGraph#引用#NXgraph#论文ForeGraph引用论文NXgraph)
(Frog#引用#CuSha#论文Frog引用论文CuSha)
(Frog#引用#Ligra#论文Frog引用论文Ligra)
(Frog#引用#X-Stream#论文Frog引用论文X-Stream)
(G-Sdstre#引用#Chaos#论文G-Sdstre引用论文Chaos)
(G-Tran#引用#A1#论文G-Tran引用论文A1)
(G-Tran#引用#PowerLyra#论文G-Tran引用论文PowerLyra)
(G-Tran#引用#SGraph#论文G-Tran引用论文SGraph)
(G-Tran#引用#TigerGraph#论文G-Tran引用数据库TigerGraph)
(G-Tran#引用#Weaver#论文G-Tran引用论文Weaver)
(GBASE#引用#Pregel#论文GBASE引用论文Pregel)
(GFlink#引用#Medusa#论文GFlink引用论文Medusa)

### 8.3 论文图谱子图-3
Entities:
(GGraph#An Efficient Structure-Aware Approach for Iterative Graph Processing)
(FBSGraph#Accelerating Asynchronous Graph Processing via Forward and Backward Sweeping)
(GrapH#Traffic-Aware Graph Processing)
(GraphA#An efficient ReRAM-based architecture dst accelerate large scale graph processing)
(GraphM#An Efficient Sdstrage System for High Throughput of Concurrent Graph Processing)
(HGraph#I/O-efficient Distributed and Iterative Graph Computing by Hybrid Pushing/Pulling)
(HUS-Graph#I/O-Efficient Out-of-Core Graph Processing with Hybrid Update Strategy)
(GPOP#A scalable cache- and memory-efficient framework for Graph Processing Over Partitions)
(GoFFish#A Sub-graph Centric Framework for Large-Scale Graph Analytics)
(GridGraph#Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning)
(GPS#A Graph Processing System)
(iMapReduce#A Distributed Computing Framework for Iterative Computation)
(Gradoop#Gradoop)
(Raphdstry#Streaming Analysis Of Distributed Temporal Graphs)
(GRAM#Scaling Graph Computation dst the Trillions)
(PowerLyra#Differentiated Graph Computation and Partitioning on Skewed Graphs)
(GRAPE#Parallelizing Sequential Graph Computations)
(Blogel#A Block-Centric Framework for Distributed Computation on Real-World Graphs)
(GStream#A Graph Streaming Processing Method for Large-scale Graphs on GPUs)
(CuSha#Vertex-Centric Graph Processing on GPUs)
(Trinity#A Distributed Graph Engine on a Memory Cloud)
(TurboGraph#A Fast Parallel Graph Engine Handling Billion-scale Graphs in a Single PC)
(Garaph#Efficient GPU-accelerated Graph Processing on a Single Machine with Balanced Replication)
(Chaos#Scale-out Graph Processing src Secondary Sdstrage)
(Gemini#A Computation-Centric Distributed Graph Processing System)
(GasCL#A Vertex-Centric Graph Model for GPUs)
(Medusa#Simplified Graph Processing on GPUs)
(X-Stream#Edge-centric Graph Processing using Streaming Partitions)
(GeaFlow#A Graph Extended and Accelerated Dataflow System)
(ByteGraph#A High-Performance Distributed Graph Database in ByteDance)
(RisGraph#A Real-Time Streaming System for Evolving Graphs dst Support Sub-millisecond Per-update Analysis at Millions Ops/s)
(TuGraph#DB)
(iGraph#an incremental data processing system for dynamic graph)
(LazyGraph#Lazy Data Coherency for Replicas in Distributed Graph-Parallel Computation)
(Giraph Unchained#Barrierless Asynchronous Parallel Execution in Pregel-like Graph Processing Systems)
(Pregelix#Big(ger) Graph Analytics on A Dataflow Engine)
(Giraph#DB)
(Graph Database#DB)
(GraPU#Accelerate Streaming Graph Analysis through Preprocessing Buffered Updates)
(KickStarter#Fast and Accurate Computations on Streaming Graphs via Trimmed Approximations)
(Tigr#Transforming Irregular Graphs for GPU-Friendly Graph Processing)
(Version Traveler#Fast and Memory-Efficient Version Switching in Graph Processing Systems)
(Wonderland#A Novel Abstraction-Based Out-Of-Core Graph Processing System)
(Zorro#Zero-Cost Reactive Failure Recovery in Distributed Graph Processing)
(GraVF#A Vertex-Centric Distributed Graph Processing Framework on FPGAs)
(GraphGen#An FPGA Framework for Vertex-Centric Graph Computation)
(Graph3S#A Simple, Speedy and Scalable Distributed Graph Processing System)
(Graphene#Fine-Grained IO Management for Graph Computing)
(HybridGraph#Hybrid Pulling:Pushing for I:O-Efficient Distributed and Iterative Graph Computing)
(GFlink#An In-Memory Computing Architecture on Heterogeneous CPU-GPU Clusters for Big Data)
(GraphR#Accelerating Graph Processing Using ReRAM)
(GraphABCD#Scaling Out Graph Analytics with Asynchronous Block Coordinate Descent)
(CLIP#A Disk I/O Focused Parallel Out-of-core Graph Processing System)
(ForeGraph#Exploring Large-scale Graph Processing on Multi-FPGA Architecture)
(GraphP#Reducing Communication for PIM-based Graph Processing with Efficient Data Partition)
(GraphBolt#Dependency-Driven Synchronous Processing of Streaming Graphs)
(GraphIn#An Online High Performance Incremental Graph Processing Framework)
(GraphBuilder#Scalable Graph ETL Framework)
(GraphCP#An I/O-Efficient Concurrent Graph Processing Framework)
(CGraph#A correlations-aware approach for efficient concurrent iterative graph processing)
(GraphZ#Improving the Performance of Large-Scale Graph Analytics on Small-Scale Machines)
(Seraph#an Efficient, Low-cost System for Concurrent Graph Processing)
(VENUS#A System for Streamlined Graph Computation on a Single PC)
(GraphCage#Cache Aware Graph Processing on GPUs)
(GraphMat#High performance graph analytics made productive)

Relationships:
(GGraph#引用#FBSGraph#论文GGraph引用论文FBSGraph)
(GGraph#引用#GrapH#论文GGraph引用论文GrapH)
(GGraph#引用#GraphA#论文GGraph引用论文GraphA)
(GGraph#引用#GraphM#论文GGraph引用论文GraphM)
(GGraph#引用#HGraph#论文GGraph引用论文HGraph)
(GGraph#引用#HUS-Graph#论文GGraph引用论文HUS-Graph)
(GrapH#引用#GridGraph#论文GrapH引用论文GridGraph)
(GraphA#引用#GFlink#论文GraphA引用论文GFlink)
(GraphA#引用#GraphR#论文GraphA引用论文GraphR)
(GPOP#引用#GoFFish#论文GPOP引用论文GoFFish)
(GPOP#引用#GridGraph#论文GPOP引用论文GridGraph)
(GoFFish#引用#Trinity#论文GoFFish引用论文Trinity)
(GPS#引用#iMapReduce#论文GPS引用论文iMapReduce)
(Gradoop#引用#Raphdstry#论文Gradoop引用论文Raphdstry)
(GRAM#引用#PowerLyra#论文GRAM引用论文PowerLyra)
(GRAPE#引用#Blogel#论文GRAPE引用论文Blogel)
(GStream#引用#CuSha#论文GStream引用论文CuSha)
(GStream#引用#Trinity#论文GStream引用论文Trinity)
(GStream#引用#TurboGraph#论文GStream引用论文TurboGraph)
(Garaph#引用#Chaos#论文Garaph引用论文Chaos)
(Garaph#引用#CuSha#论文Garaph引用论文CuSha)
(Garaph#引用#GStream#论文Garaph引用论文GStream)
(Garaph#引用#Gemini#论文Garaph引用论文Gemini)
(Gemini#引用#GridGraph#论文Gemini引用论文GridGraph)
(Gemini#引用#LazyGraph#论文Gemini引用论文LazyGraph)
(GasCL#引用#Medusa#论文GasCL引用论文Medusa)
(GasCL#引用#X-Stream#论文GasCL引用论文X-Stream)
(GeaFlow#引用#ByteGraph#论文GeaFlow引用论文ByteGraph)
(GeaFlow#引用#RisGraph#论文GeaFlow引用论文RisGraph)
(GeaFlow#引用#TuGraph#论文GeaFlow引用数据库TuGraph)
(GeaFlow#引用#iGraph#论文GeaFlow引用论文iGraph)
(Giraph Unchained#引用#Pregelix#论文Giraph Unchained引用论文Pregelix)
(Giraph#引用#Graph Database#数据库Giraph引用数据库Graph Database)
(GraPU#引用#Gemini#论文GraPU引用论文Gemini)
(GraPU#引用#GoFFish#论文GraPU引用论文GoFFish)
(GraPU#引用#KickStarter#论文GraPU引用论文KickStarter)
(GraPU#引用#Tigr#论文GraPU引用论文Tigr)
(GraPU#引用#Version Traveler#论文GraPU引用论文Version Traveler)
(GraPU#引用#Wonderland#论文GraPU引用论文Wonderland)
(GraPU#引用#Zorro#论文GraPU引用论文Zorro)
(GraVF#引用#GraphGen#论文GraVF引用论文GraphGen)
(Graph3S#引用#Graphene#论文Graph3S引用论文Graphene)
(Graph3S#引用#HybridGraph#论文Graph3S引用论文HybridGraph)
(GraphABCD#引用#CLIP#论文GraphABCD引用论文CLIP)
(GraphABCD#引用#ForeGraph#论文GraphABCD引用论文ForeGraph)
(GraphABCD#引用#GraphP#论文GraphABCD引用论文GraphP)
(GraphABCD#引用#Tigr#论文GraphABCD引用论文Tigr)
(GraphABCD#引用#Wonderland#论文GraphABCD引用论文Wonderland)
(GraphBolt#引用#GraphIn#论文GraphBolt引用论文GraphIn)
(GraphBolt#引用#KickStarter#论文GraphBolt引用论文KickStarter)
(GraphBuilder#引用#Trinity#论文GraphBuilder引用论文Trinity)
(GraphCP#引用#CGraph#论文GraphCP引用论文CGraph)
(GraphCP#引用#GraphM#论文GraphCP引用论文GraphM)
(GraphCP#引用#GraphZ#论文GraphCP引用论文GraphZ)
(GraphCP#引用#Seraph#论文GraphCP引用论文Seraph)
(GraphCP#引用#VENUS#论文GraphCP引用论文VENUS)
(GraphCage#引用#GraphMat#论文GraphCage引用论文GraphMat)

### 8.4 论文图谱子图-4
Entities:
(GraphCage#Cache Aware Graph Processing on GPUs)
(GraphPhi#Efficient Parallel Graph Processing on Emerging Throughput-oriented Architectures)
(GraphChi#Large-Scale Graph Computation on Just a PC)
(GPS#A Graph Processing System)
(PowerGraph#Distributed Graph-Parallel Computation on Natural Graphs)
(GraphD#Distributed Vertex-Centric Graph Processing Beyond the Memory Limit)
(Blogel#A Block-Centric Framework for Distributed Computation on Real-World Graphs)
(G-Sdstre#High-Performance Graph Sdstre for Trillion-Edge Processing)
(Pregelix#Big(ger) Graph Analytics on A Dataflow Engine)
(VENUS#A System for Streamlined Graph Computation on a Single PC)
(GraphDuo#A Dual-Model Graph Processing Framework)
(GrapH#Traffic-Aware Graph Processing)
(GraphIn#An Online High Performance Incremental Graph Processing Framework)
(GraphP#Reducing Communication for PIM-based Graph Processing with Efficient Data Partition)
(Wonderland#A Novel Abstraction-Based Out-Of-Core Graph Processing System)
(GraphFly#Efficient Asynchronous Streaming Graphs Processing via Dependency-Flow)
(LiveGraph#A Transactional Graph Sdstrage System with Purely Sequential Adjacency List Scans)
(ScalaGraph#A Scalable Acceleradstr for Massively Parallel Graph Processing)
(Tripoline#Generalized Incremental Graph Processing via Graph Triangle Inequality)
(GraphGen#An FPGA Framework for Vertex-Centric Graph Computation)
(GraphGrind#addressing load imbalance of graph partitioning)
(Ligra#A Lightweight Graph Processing Framework for Shared Memory)
(GraphH(1)#High Performance Big Graph Analytics in Small Clusters)
(Chaos#Scale-out Graph Processing src Secondary Sdstrage)
(MOCgraph#Scalable Distributed Graph Processing Using Message Online Computing)
(GraphH(2)#A Processing-in-Memory Architecture for Large-scale Graph Processing)
(Garaph#Efficient GPU-accelerated Graph Processing on a Single Machine with Balanced Replication)
(GraphPIM#Enabling Instruction-Level PIM Offloading in Graph Computing Frameworks)
(NXgraph#An Efficient Graph Processing System on a Single Machine*)
(GraphIA#An In-situ Acceleradstr for Large-scale Graph Processing)
(Gemini#A Computation-Centric Distributed Graph Processing System)
(GraphMat#High performance graph analytics made productive)
(GraphReduce#Processing Large-Scale Graphs on Acceleradstr-Based Systems)
(STINGER#High Performance Data Structure for Streaming Graphs)
(GraphLab#A New Framework For Parallel Machine Learning)
(Pregel#A System for Large-Scale Graph Processing)
(GraphM#An Efficient Sdstrage System for High Throughput of Concurrent Graph Processing)
(DiGraph#An Efficient Path-based Iterative Directed Graph Processing System on Multiple GPUs)
(GraphR#Accelerating Graph Processing Using ReRAM)
(Lumos#Dependency-Driven Disk-based Graph Processing)
(GraphMP(1)#An Efficient Semi-External-Memory Big Graph Processing System on a Single Machine)
(MOSAIC#Processing a Trillion-Edge Graph on a Single Machine)
(GraphMP(2)#I/O-Efficient Big Graph Analytics on a Single Commodity Machine)
(GraphMap#scalable iterative graph processing using NoSQL)
(GraphTwist#Fast Iterative Graph Computation with Two-tier Optimizations)
(GraphX#A Resilient Distributed Graph System on Spark)
(MapGraph#A High Level API for Fast Development of High Performance Graph Analytics on GPUs)
(GraphOne#A Data Sdstre for Real-time Analytics on Evolving Graphs)
(GBASE#A Scalable and General Graph Management System)
(GraPU#Accelerate Streaming Graph Analysis through Preprocessing Buffered Updates)
(GraphBolt#Dependency-Driven Synchronous Processing of Streaming Graphs)
(Graphene#Fine-Grained IO Management for Graph Computing)
(TIDE#Dynamic Interaction Graphs with Probabilistic Edge Decay)
(GridGraph#Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning)
(GraphPEG#Accelerating Graph Processing on GPUs)
(Tigr#Transforming Irregular Graphs for GPU-Friendly Graph Processing)

Relationships:
(GraphCage#引用#GraphPhi#论文GraphCage引用论文GraphPhi)
(GraphChi#引用#GPS#论文GraphChi引用论文GPS)
(GraphChi#引用#PowerGraph#论文GraphChi引用论文PowerGraph)
(GraphD#引用#Blogel#论文GraphD引用论文Blogel)
(GraphD#引用#G-Sdstre#论文GraphD引用论文G-Sdstre)
(GraphD#引用#Pregelix#论文GraphD引用论文Pregelix)
(GraphD#引用#VENUS#论文GraphD引用论文VENUS)
(GraphDuo#引用#GrapH#论文GraphDuo引用论文GrapH)
(GraphDuo#引用#GraphD#论文GraphDuo引用论文GraphD)
(GraphDuo#引用#GraphIn#论文GraphDuo引用论文GraphIn)
(GraphDuo#引用#GraphP#论文GraphDuo引用论文GraphP)
(GraphDuo#引用#Wonderland#论文GraphDuo引用论文Wonderland)
(GraphIn#引用#GraphMat#论文GraphIn引用论文GraphMat)
(GraphIn#引用#GraphReduce#论文GraphIn引用论文GraphReduce)
(GraphIn#引用#STINGER#论文GraphIn引用论文STINGER)
(GraphP#引用#GraphPIM#论文GraphP引用论文GraphPIM)
(GraphP#引用#GridGraph#论文GraphP引用论文GridGraph)
(GraphFly#引用#LiveGraph#论文GraphFly引用论文LiveGraph)
(GraphFly#引用#ScalaGraph#论文GraphFly引用论文ScalaGraph)
(GraphFly#引用#Tripoline#论文GraphFly引用论文Tripoline)
(GraphGen#引用#GraphChi#论文GraphGen引用论文GraphChi)
(GraphGrind#引用#Ligra#论文GraphGrind引用论文Ligra)
(GraphH(1)#引用#Chaos#论文GraphH(1)引用论文Chaos)
(GraphH(1)#引用#MOCgraph#论文GraphH(1)引用论文MOCgraph)
(GraphH(2)#引用#Garaph#论文GraphH(2)引用论文Garaph)
(GraphH(2)#引用#GraphPIM#论文GraphH(2)引用论文GraphPIM)
(GraphH(2)#引用#MOCgraph#论文GraphH(2)引用论文MOCgraph)
(GraphH(2)#引用#NXgraph#论文GraphH(2)引用论文NXgraph)
(GraphPIM#引用#GraphChi#论文GraphPIM引用论文GraphChi)
(GraphIA#引用#Gemini#论文GraphIA引用论文Gemini)
(GraphIA#引用#GraphH(2)#论文GraphIA引用论文GraphH(2)
(GraphIA#引用#GraphP#论文GraphIA引用论文GraphP)
(GraphIA#引用#NXgraph#论文GraphIA引用论文NXgraph)
(GraphMat#引用#GraphX#论文GraphMat引用论文GraphX)
(GraphMat#引用#MapGraph#论文GraphMat引用论文MapGraph)
(GraphLab#引用#Pregel#论文GraphLab引用论文Pregel)
(GraphM#引用#DiGraph#论文GraphM引用论文DiGraph)
(GraphM#引用#GraphR#论文GraphM引用论文GraphR)
(GraphM#引用#Lumos#论文GraphM引用论文Lumos)
(GraphMP(1)#引用#GraphD#论文GraphMP(1)引用论文GraphD)
(GraphMP(1)#引用#GraphH(1)#论文GraphMP(1)引用论文GraphH(1)
(GraphMP(1)#引用#MOSAIC#论文GraphMP(1)引用论文MOSAIC)
(GraphMP(2)#引用#GraphMP(1)#论文GraphMP(2)引用论文GraphMP(1)
(GraphMap#引用#DiGraph#论文GraphMap引用论文DiGraph)
(GraphMap#引用#GraphD#论文GraphMap引用论文GraphD)
(GraphMap#引用#GraphTwist#论文GraphMap引用论文GraphTwist)
(GraphMap#引用#Pregelix#论文GraphMap引用论文Pregelix)
(GraphOne#引用#GBASE#论文GraphOne引用论文GBASE)
(GraphOne#引用#GraPU#论文GraphOne引用论文GraPU)
(GraphOne#引用#GraphBolt#论文GraphOne引用论文GraphBolt)
(GraphOne#引用#GraphD#论文GraphOne引用论文GraphD)
(GraphOne#引用#Graphene#论文GraphOne引用论文Graphene)
(GraphOne#引用#MOSAIC#论文GraphOne引用论文MOSAIC)
(GraphOne#引用#TIDE#论文GraphOne引用论文TIDE)
(GraphPEG#引用#DiGraph#论文GraphPEG引用论文DiGraph)
(GraphPEG#引用#GraphCage#论文GraphPEG引用论文GraphCage)
(GraphPEG#引用#Tigr#论文GraphPEG引用论文Tigr)

### 8.5 论文图谱子图-5
Entities:
(GraphPhi#Efficient Parallel Graph Processing on Emerging Throughput-oriented Architectures)
(GraphReduce#Processing Large-Scale Graphs on Acceleradstr-Based Systems)
(Graphie#Large-Scale Asynchronous Graph Traversals on Just a GPU)
(GraphPulse#An Event-Driven Hardware Acceleradstr for Asynchronous Graph Processing)
(FBSGraph#Accelerating Asynchronous Graph Processing via Forward and Backward Sweeping)
(GraphPIM#Enabling Instruction-Level PIM Offloading in Graph Computing Frameworks)
(Maiter#An Asynchronous Graph Processing Framework for Delta-based Accumulative Iterative Computation)
(GraphQ#Graph Query Processing with Abstraction Refinement)
(X-Stream#Edge-centric Graph Processing using Streaming Partitions)
(GraphR#Accelerating Graph Processing Using ReRAM)
(GridGraph#Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning)
(CuSha#Vertex-Centric Graph Processing on GPUs)
(MapGraph#A High Level API for Fast Development of High Performance Graph Analytics on GPUs)
(GraphSD#A State and Dependency aware Out-of-Core Graph Processing System)
(Giraph Unchained#Barrierless Asynchronous Parallel Execution in Pregel-like Graph Processing Systems)
(GraphZ#Improving the Performance of Large-Scale Graph Analytics on Small-Scale Machines)
(Lumos#Dependency-Driven Disk-based Graph Processing)
(MultiLogVC#Efficient Out-of-Core Graph Processing Framework for Flash Sdstrage)
(GraphScSh#Efficient I/O Scheduling and Graph Sharing for Concurrent Graph Processing)
(CGraph#A correlations-aware approach for efficient concurrent iterative graph processing)
(HGraph#I/O-efficient Distributed and Iterative Graph Computing by Hybrid Pushing/Pulling)
(GraphScope#A Unified Engine For Big Graph Processing)
(A1#A Distributed In-Memory Graph Database)
(EmptyHeaded#A Relational Engine for Graph Processing)
(FENNEL#Streaming Graph Partitioning for Massive Scale Graphs)
(Gemini#A Computation-Centric Distributed Graph Processing System)
(GraphTides#A Framework for Evaluating Stream-based Graph Processing Platforms)
(Chronograph#A Distributed Processing Platform for Online and Batch Computations on Event-sourced Graphs)
(GoFFish#A Sub-graph Centric Framework for Large-Scale Graph Analytics)
(KickStarter#Fast and Accurate Computations on Streaming Graphs via Trimmed Approximations)
(Weaver#A High-Performance, Transactional Graph Database Based on Refinable Timestamps)
(GraphTinker#A High Performance Data Structure for Dynamic Graph Processing)
(GraphIn#An Online High Performance Incremental Graph Processing Framework)
(GraphTune#An Efficient Dependency-Aware Substrate dst Alleviate Irregularity in Concurrent Graph Processing)
(GraphM#An Efficient Sdstrage System for High Throughput of Concurrent Graph Processing)
(GraphTwist#Fast Iterative Graph Computation with Two-tier Optimizations)
(GraphX#A Resilient Distributed Graph System on Spark)
(TurboGraph#A Fast Parallel Graph Engine Handling Billion-scale Graphs in a Single PC)
(PowerGraph#Distributed Graph-Parallel Computation on Natural Graphs)
(Graphene#Fine-Grained IO Management for Graph Computing)
(Chaos#Scale-out Graph Processing src Secondary Sdstrage)
(GRE#A Graph Runtime Engine for Large-Scale Distributed Graph-Parallel Applications)
(Wonderland#A Novel Abstraction-Based Out-Of-Core Graph Processing System)
(Graphflow#An Active Graph Database)
(Neo4j#DB)
(PathGraph#A Path Centric Graph Processing System)
(PowerLyra#Differentiated Graph Computation and Partitioning on Skewed Graphs)
(Graspan#A Single-machine Disk-based Graph System for Interprocedural Static Analyses of Large-scale Systems Code)
(ASPIRE#Exploiting Asynchronous Parallelism in Iterative Algorithms using a Relaxed Consistency based DSM)
(Grasper#A High Performance Distributed System for OLAP on Property Graphs)
(GraphD#Distributed Vertex-Centric Graph Processing Beyond the Memory Limit)
(Mizan#A System for Dynamic Load Balancing in Large-scale Graph Processing)
(FlashGraph#Processing Billion-Node Graphs on an Array of Commodity SSDs)
(LightGraph#Lighten Communication in Distributed Graph-Parallel Processing)
(MMap#Fast Billion-Scale Graph Computation on a PC via Memory Mapping)
(Groute#Asynchronous Multi-GPU Programming Model with Applications dst Large-scale Graph Processing)
(Blogel#A Block-Centric Framework for Distributed Computation on Real-World Graphs)
(GBASE#A Scalable and General Graph Management System)
(LFGraph#Simple and Fast Distributed Graph Analytics)
(Pregelix#Big(ger) Graph Analytics on A Dataflow Engine)
(Seraph#an Efficient, Low-cost System for Concurrent Graph Processing)

Relationships:
(GraphPhi#引用#GraphReduce#论文GraphPhi引用论文GraphReduce)
(GraphPhi#引用#Graphie#论文GraphPhi引用论文Graphie)
(GraphReduce#引用#CuSha#论文GraphReduce引用论文CuSha)
(GraphReduce#引用#MapGraph#论文GraphReduce引用论文MapGraph)
(GraphReduce#引用#X-Stream#论文GraphReduce引用论文X-Stream)
(Graphie#引用#PathGraph#论文Graphie引用论文PathGraph)
(Graphie#引用#PowerLyra#论文Graphie引用论文PowerLyra)
(GraphPulse#引用#FBSGraph#论文GraphPulse引用论文FBSGraph)
(GraphPulse#引用#GraphPIM#论文GraphPulse引用论文GraphPIM)
(GraphPulse#引用#Maiter#论文GraphPulse引用论文Maiter)
(GraphQ#引用#X-Stream#论文GraphQ引用论文X-Stream)
(GraphR#引用#GraphPIM#论文GraphR引用论文GraphPIM)
(GraphR#引用#GridGraph#论文GraphR引用论文GridGraph)
(GridGraph#引用#FlashGraph#论文GridGraph引用论文FlashGraph)
(GridGraph#引用#GraphQ#论文GridGraph引用论文GraphQ)
(GridGraph#引用#LightGraph#论文GridGraph引用论文LightGraph)
(GridGraph#引用#MMap#论文GridGraph引用论文MMap)
(GridGraph#引用#PowerLyra#论文GridGraph引用论文PowerLyra)
(GraphSD#引用#Giraph Unchained#论文GraphSD引用论文Giraph Unchained)
(GraphSD#引用#GraphZ#论文GraphSD引用论文GraphZ)
(GraphSD#引用#Lumos#论文GraphSD引用论文Lumos)
(GraphSD#引用#MultiLogVC#论文GraphSD引用论文MultiLogVC)
(GraphZ#引用#Gemini#论文GraphZ引用论文Gemini)
(GraphScSh#引用#CGraph#论文GraphScSh引用论文CGraph)
(GraphScSh#引用#HGraph#论文GraphScSh引用论文HGraph)
(HGraph#引用#Blogel#论文HGraph引用论文Blogel)
(HGraph#引用#GBASE#论文HGraph引用论文GBASE)
(HGraph#引用#GRE#论文HGraph引用论文GRE)
(HGraph#引用#LFGraph#论文HGraph引用论文LFGraph)
(HGraph#引用#Pregelix#论文HGraph引用论文Pregelix)
(HGraph#引用#Seraph#论文HGraph引用论文Seraph)
(GraphScope#引用#A1#论文GraphScope引用论文A1)
(GraphScope#引用#EmptyHeaded#论文GraphScope引用论文EmptyHeaded)
(GraphScope#引用#FENNEL#论文GraphScope引用论文FENNEL)
(GraphScope#引用#Gemini#论文GraphScope引用论文Gemini)
(GraphTides#引用#Chronograph#论文GraphTides引用论文Chronograph)
(GraphTides#引用#GoFFish#论文GraphTides引用论文GoFFish)
(GraphTides#引用#KickStarter#论文GraphTides引用论文KickStarter)
(GraphTides#引用#Weaver#论文GraphTides引用论文Weaver)
(GraphTinker#引用#GraphIn#论文GraphTinker引用论文GraphIn)
(GraphTune#引用#CGraph#论文GraphTune引用论文CGraph)
(GraphTune#引用#Giraph Unchained#论文GraphTune引用论文Giraph Unchained)
(GraphTune#引用#GraphM#论文GraphTune引用论文GraphM)
(GraphTwist#引用#GraphX#论文GraphTwist引用论文GraphX)
(GraphTwist#引用#TurboGraph#论文GraphTwist引用论文TurboGraph)
(GraphX#引用#PowerGraph#论文GraphX引用论文PowerGraph)
(Graphene#引用#Chaos#论文Graphene引用论文Chaos)
(Graphene#引用#GRE#论文Graphene引用论文GRE)
(Graphene#引用#Maiter#论文Graphene引用论文Maiter)
(Graphene#引用#Wonderland#论文Graphene引用论文Wonderland)
(GRE#引用#Mizan#论文GRE引用论文Mizan)
(GRE#引用#X-Stream#论文GRE引用论文X-Stream)
(Graphflow#引用#Neo4j#论文Graphflow引用数据库Neo4j)
(Graspan#引用#ASPIRE#论文Graspan引用论文ASPIRE)
(Graspan#引用#GridGraph#论文Graspan引用论文GridGraph)
(Grasper#引用#GraphD#论文Grasper引用论文GraphD)
(Groute#引用#Graphie#论文Groute引用论文Graphie)

### 8.6 论文图谱子图-6
Entities:
(HPGraph#A High Parallel Graph Processing System Based on Flash Array)
(MMap#Fast Billion-Scale Graph Computation on a PC via Memory Mapping)
(HUS-Graph#I/O-Efficient Out-of-Core Graph Processing with Hybrid Update Strategy)
(BlitzG#Exploiting high-bandwidth networks for fast graph processing)
(Gemini#A Computation-Centric Distributed Graph Processing System)
(Graphene#Fine-Grained IO Management for Graph Computing)
(LCC-Graph#A High-Performance Graph-Processing Framework with Low Communication Costs)
(HipG#Parallel Processing of Large-Scale Graphs)
(Pregel#A System for Large-Scale Graph Processing)
(HitGraph#High-throughput Graph Processing Framework on FPGA)
(GraphMat#High performance graph analytics made productive)
(Graphie#Large-Scale Asynchronous Graph Traversals on Just a GPU)
(HyVE#Hybrid Vertex-Edge Memory Hierarchy for Energy-Efficient Graph Processing)
(ThunderGP#HLS-based graph processing framework on FPGAs)
(HotGraph#Efficient Asynchronous Processing for Real-World Graphs)
(GridGraph#Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning)
(HyTGraph#GPU-Accelerated Graph Processing with Hybrid Transfer Management)
(DiGraph#An Efficient Path-based Iterative Directed Graph Processing System on Multiple GPUs)
(Scaph#Scalable GPU-Accelerated Graph Processing with Value-Driven Differential Scheduling)
(Subway#Minimizing Data Transfer during Out-of-GPU-Memory Graph Processing)
(GraphR#Accelerating Graph Processing Using ReRAM)
(NXgraph#An Efficient Graph Processing System on a Single Machine*)
(HybridGraph#Hybrid Pulling:Pushing for I:O-Efficient Distributed and Iterative Graph Computing)
(GraphD#Distributed Vertex-Centric Graph Processing Beyond the Memory Limit)
(GRE#A Graph Runtime Engine for Large-Scale Distributed Graph-Parallel Applications)
(ImmortalGraph#A System for Sdstrage and Analysis of Temporal Graphs)
(PowerLyra#Differentiated Graph Computation and Partitioning on Skewed Graphs)
(JanusGraph#DB)
(Graph Database#DB)
(JetStream#Graph Analytics on Streaming Data with Event-Driven Hardware Acceleradstr)
(ForeGraph#Exploring Large-scale Graph Processing on Multi-FPGA Architecture)
(GraphIn#An Online High Performance Incremental Graph Processing Framework)
(GraphP#Reducing Communication for PIM-based Graph Processing with Efficient Data Partition)
(GraphPulse#An Event-Driven Hardware Acceleradstr for Asynchronous Graph Processing)
(Tripoline#Generalized Incremental Graph Processing via Graph Triangle Inequality)
(Version Traveler#Fast and Memory-Efficient Version Switching in Graph Processing Systems)
(KickStarter#Fast and Accurate Computations on Streaming Graphs via Trimmed Approximations)
(Chaos#Scale-out Graph Processing src Secondary Sdstrage)
(Graspan#A Single-machine Disk-based Graph System for Interprocedural Static Analyses of Large-scale Systems Code)
(STINGER#High Performance Data Structure for Streaming Graphs)
(dstrnado#A System For Real-Time Iterative Analysis Over Evolving Data)
(Kineograph#Taking the Pulse of a Fast-Changing and Connected World)
(HaLoop#Efficient Iterative Data Processing on Large Clusters)
(Neo4j#DB)
(L-PowerGraph#a lightweight distributed graph-parallel communication mechanism)
(GrapH#Traffic-Aware Graph Processing)
(Giraph Unchained#Barrierless Asynchronous Parallel Execution in Pregel-like Graph Processing Systems)
(X-Stream#Edge-centric Graph Processing using Streaming Partitions)
(LFGraph#Simple and Fast Distributed Graph Analytics)
(Trinity#A Distributed Graph Engine on a Memory Cloud)
(LLAMA#Efficient Graph Analytics Using Large Multiversioned Arrays.)
(Chronos#A Graph Engine for Temporal Graph Analysis)
(TurboGraph#A Fast Parallel Graph Engine Handling Billion-scale Graphs in a Single PC)
(LOSC#Efficient Out-of-Core Graph Processing with Locality-optimized Subgraph Construction)
(LSGraph#A Locality-centric High-performance Streaming Graph Engine)
(DepGraph#A Dependency-Driven Acceleradstr for Efficient Iterative Graph Processing)
(LiveGraph#A Transactional Graph Sdstrage System with Purely Sequential Adjacency List Scans)
(LargeGraph#An Efficient Dependency-Aware GPU-Accelerated Large-Scale Graph Processing)
(LazyGraph#Lazy Data Coherency for Replicas in Distributed Graph-Parallel Computation)
(Frog#Asynchronous graph processing on GPU with hybrid coloring model)
(GRAM#Scaling Graph Computation dst the Trillions)
(Medusa#Simplified Graph Processing on GPUs)
(NScale#Neighborhood-centric Analytics on Large Graphs)
(PGX.D#A Fast Distributed Graph Processing Engine)
(LightGraph#Lighten Communication in Distributed Graph-Parallel Processing)
(Ligra#A Lightweight Graph Processing Framework for Shared Memory)
(Giraph#DB)
(GraphChi#Large-Scale Graph Computation on Just a PC)

Relationships:
(HPGraph#引用#MMap#论文HPGraph引用论文MMap)
(HUS-Graph#引用#BlitzG#论文HUS-Graph引用论文BlitzG)
(HUS-Graph#引用#Gemini#论文HUS-Graph引用论文Gemini)
(HUS-Graph#引用#Graphene#论文HUS-Graph引用论文Graphene)
(HUS-Graph#引用#LCC-Graph#论文HUS-Graph引用论文LCC-Graph)
(LCC-Graph#引用#Giraph Unchained#论文LCC-Graph引用论文Giraph Unchained)
(LCC-Graph#引用#X-Stream#论文LCC-Graph引用论文X-Stream)
(HipG#引用#Pregel#论文HipG引用论文Pregel)
(HitGraph#引用#GraphMat#论文HitGraph引用论文GraphMat)
(HitGraph#引用#Graphie#论文HitGraph引用论文Graphie)
(HitGraph#引用#HyVE#论文HitGraph引用论文HyVE)
(HitGraph#引用#ThunderGP#论文HitGraph引用论文ThunderGP)
(HyVE#引用#GraphR#论文HyVE引用论文GraphR)
(HyVE#引用#NXgraph#论文HyVE引用论文NXgraph)
(HotGraph#引用#GridGraph#论文HotGraph引用论文GridGraph)
(HyTGraph#引用#DiGraph#论文HyTGraph引用论文DiGraph)
(HyTGraph#引用#Scaph#论文HyTGraph引用论文Scaph)
(HyTGraph#引用#Subway#论文HyTGraph引用论文Subway)
(HybridGraph#引用#GraphD#论文HybridGraph引用论文GraphD)
(HybridGraph#引用#GRE#论文HybridGraph引用论文GRE)
(ImmortalGraph#引用#PowerLyra#论文ImmortalGraph引用论文PowerLyra)
(JanusGraph#引用#Graph Database#数据库JanusGraph引用数据库Graph Database)
(JetStream#引用#ForeGraph#论文JetStream引用论文ForeGraph)
(JetStream#引用#GraphIn#论文JetStream引用论文GraphIn)
(JetStream#引用#GraphP#论文JetStream引用论文GraphP)
(JetStream#引用#GraphPulse#论文JetStream引用论文GraphPulse)
(JetStream#引用#Tripoline#论文JetStream引用论文Tripoline)
(JetStream#引用#Version Traveler#论文JetStream引用论文Version Traveler)
(KickStarter#引用#Chaos#论文KickStarter引用论文Chaos)
(KickStarter#引用#Graspan#论文KickStarter引用论文Graspan)
(KickStarter#引用#STINGER#论文KickStarter引用论文STINGER)
(KickStarter#引用#dstrnado#论文KickStarter引用论文dstrnado)
(Kineograph#引用#HaLoop#论文Kineograph引用论文HaLoop)
(Kineograph#引用#Neo4j#论文Kineograph引用数据库Neo4j)
(Kineograph#引用#Pregel#论文Kineograph引用论文Pregel)
(L-PowerGraph#引用#GraphD#论文L-PowerGraph引用论文GraphD)
(L-PowerGraph#引用#GrapH#论文L-PowerGraph引用论文GrapH)
(LFGraph#引用#Trinity#论文LFGraph引用论文Trinity)
(LLAMA#引用#Chronos#论文LLAMA引用论文Chronos)
(LLAMA#引用#TurboGraph#论文LLAMA引用论文TurboGraph)
(LOSC#引用#Gemini#论文LOSC引用论文Gemini)
(LOSC#引用#LCC-Graph#论文LOSC引用论文LCC-Graph)
(LSGraph#引用#DepGraph#论文LSGraph引用论文DepGraph)
(LSGraph#引用#GraphPulse#论文LSGraph引用论文GraphPulse)
(LSGraph#引用#LiveGraph#论文LSGraph引用论文LiveGraph)
(LSGraph#引用#Tripoline#论文LSGraph引用论文Tripoline)
(LargeGraph#引用#DepGraph#论文LargeGraph引用论文DepGraph)
(LazyGraph#引用#Frog#论文LazyGraph引用论文Frog)
(LazyGraph#引用#GRAM#论文LazyGraph引用论文GRAM)
(LazyGraph#引用#LLAMA#论文LazyGraph引用论文LLAMA)
(LazyGraph#引用#Medusa#论文LazyGraph引用论文Medusa)
(LazyGraph#引用#NScale#论文LazyGraph引用论文NScale)
(LazyGraph#引用#PGX.D#论文LazyGraph引用论文PGX.D)
(LightGraph#引用#Ligra#论文LightGraph引用论文Ligra)
(LightGraph#引用#TurboGraph#论文LightGraph引用论文TurboGraph)
(Ligra#引用#Giraph#论文Ligra引用数据库Giraph)
(Ligra#引用#GraphChi#论文Ligra引用论文GraphChi)

### 8.7 论文图谱子图-7
Entities:
(LiveGraph#A Transactional Graph Sdstrage System with Purely Sequential Adjacency List Scans)
(GraphOne#A Data Sdstre for Real-time Analytics on Evolving Graphs)
(ImmortalGraph#A System for Sdstrage and Analysis of Temporal Graphs)
(Weaver#A High-Performance, Transactional Graph Database Based on Refinable Timestamps)
(Lumos#Dependency-Driven Disk-based Graph Processing)
(Garaph#Efficient GPU-accelerated Graph Processing on a Single Machine with Balanced Replication)
(GraphBolt#Dependency-Driven Synchronous Processing of Streaming Graphs)
(Graphene#Fine-Grained IO Management for Graph Computing)
(MOSAIC#Processing a Trillion-Edge Graph on a Single Machine)
(MMap#Fast Billion-Scale Graph Computation on a PC via Memory Mapping)
(TurboGraph#A Fast Parallel Graph Engine Handling Billion-scale Graphs in a Single PC)
(X-Stream#Edge-centric Graph Processing using Streaming Partitions)
(Maiter#An Asynchronous Graph Processing Framework for Delta-based Accumulative Iterative Computation)
(PowerGraph#Distributed Graph-Parallel Computation on Natural Graphs)
(MapGraph#A High Level API for Fast Development of High Performance Graph Analytics on GPUs)
(Medusa#Simplified Graph Processing on GPUs)
(GraphChi#Large-Scale Graph Computation on Just a PC)
(Mizan#A System for Dynamic Load Balancing in Large-scale Graph Processing)
(HipG#Parallel Processing of Large-Scale Graphs)
(Trinity#A Distributed Graph Engine on a Memory Cloud)
(MOCgraph#Scalable Distributed Graph Processing Using Message Online Computing)
(G-Sdstre#High-Performance Graph Sdstre for Trillion-Edge Processing)
(GraphReduce#Processing Large-Scale Graphs on Acceleradstr-Based Systems)
(MultiLogVC#Efficient Out-of-Core Graph Processing Framework for Flash Sdstrage)
(Wonderland#A Novel Abstraction-Based Out-Of-Core Graph Processing System)
(NGraph#Parallel Graph Processing in Hybrid Memory Systems)
(GraphGrind#addressing load imbalance of graph partitioning)
(Seraph#an Efficient, Low-cost System for Concurrent Graph Processing)
(NPGraph#An Efficient Graph Computing Model in NUMA-Based Persistent Memory Systems)
(EPGraph#An Efficient Graph Computing Model in Persistent Memory System)
(GraphMP(2)#I/O-Efficient Big Graph Analytics on a Single Commodity Machine)
(XPGraph#XPline-Friendly Persistent Memory Graph Sdstres for Large-Scale Evolving Graphs)
(NScale#Neighborhood-centric Analytics on Large Graphs)
(Naiad#A Timely Dataflow System)
(Differential dataflow#Differential dataflow)
(GraphX#A Resilient Distributed Graph System on Spark)
(Neo4j#DB)
(Graph Database#DB)
(NXgraph#An Efficient Graph Processing System on a Single Machine*)
(GBASE#A Scalable and General Graph Management System)
(GridGraph#Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning)
(PGAbB#A Block-Based Graph Processing Framework for Heterogeneous Platforms)
(PGX.D#A Fast Distributed Graph Processing Engine)
(Distributed GraphLab#A Framework for Machine Learning and Data Mining in the Cloud)
(STINGER#High Performance Data Structure for Streaming Graphs)
(PartitionedVC#Partitioned External Memory Graph Analytics Framework for SSDs)
(GraphMP(1)#An Efficient Semi-External-Memory Big Graph Processing System on a Single Machine)
(PathGraph#A Path Centric Graph Processing System)
(Pimiendst#A Vertex-Centric Graph-Processing Framework on a Single Machine)
(FOG#A Fast Out-of-Core Graph Processing Framework)
(Gemini#A Computation-Centric Distributed Graph Processing System)
(VENUS#A System for Streamlined Graph Computation on a Single PC)
(Kineograph#Taking the Pulse of a Fast-Changing and Connected World)
(PowerLyra#Differentiated Graph Computation and Partitioning on Skewed Graphs)
(Blogel#A Block-Centric Framework for Distributed Computation on Real-World Graphs)
(Chronos#A Graph Engine for Temporal Graph Analysis)
(GraphBuilder#Scalable Graph ETL Framework)
(LFGraph#Simple and Fast Distributed Graph Analytics)
(PrIter#A Distributed Framework for Prioritized Iterative Computations)
(GraphLab#A New Framework For Parallel Machine Learning)
(Pregelix#Big(ger) Graph Analytics on A Dataflow Engine)
(Quegel#A General-Purpose Query-Centric Framework for Querying Big Graphs)

Relationships:
(LiveGraph#引用#GraphOne#论文LiveGraph引用论文GraphOne)
(LiveGraph#引用#ImmortalGraph#论文LiveGraph引用论文ImmortalGraph)
(LiveGraph#引用#Weaver#论文LiveGraph引用论文Weaver)
(Lumos#引用#Garaph#论文Lumos引用论文Garaph)
(Lumos#引用#GraphBolt#论文Lumos引用论文GraphBolt)
(Lumos#引用#Graphene#论文Lumos引用论文Graphene)
(Lumos#引用#MOSAIC#论文Lumos引用论文MOSAIC)
(MOSAIC#引用#G-Sdstre#论文MOSAIC引用论文G-Sdstre)
(MOSAIC#引用#GraphReduce#论文MOSAIC引用论文GraphReduce)
(MMap#引用#TurboGraph#论文MMap引用论文TurboGraph)
(MMap#引用#X-Stream#论文MMap引用论文X-Stream)
(Maiter#引用#PowerGraph#论文Maiter引用论文PowerGraph)
(PowerGraph#引用#Distributed GraphLab#论文PowerGraph引用论文Distributed GraphLab)
(PowerGraph#引用#Kineograph#论文PowerGraph引用论文Kineograph)
(MapGraph#引用#PowerGraph#论文MapGraph引用论文PowerGraph)
(Medusa#引用#GraphChi#论文Medusa引用论文GraphChi)
(Mizan#引用#HipG#论文Mizan引用论文HipG)
(Mizan#引用#Trinity#论文Mizan引用论文Trinity)
(MOCgraph#引用#Mizan#论文MOCgraph引用论文Mizan)
(MOCgraph#引用#TurboGraph#论文MOCgraph引用论文TurboGraph)
(MultiLogVC#引用#Wonderland#论文MultiLogVC引用论文Wonderland)
(NGraph#引用#GraphGrind#论文NGraph引用论文GraphGrind)
(NGraph#引用#Seraph#论文NGraph引用论文Seraph)
(NPGraph#引用#EPGraph#论文NPGraph引用论文EPGraph)
(NPGraph#引用#GraphMP(2)#论文NPGraph引用论文GraphMP(2)
(NPGraph#引用#XPGraph#论文NPGraph引用论文XPGraph)
(NScale#引用#Trinity#论文NScale引用论文Trinity)
(Naiad#引用#Differential dataflow#论文Naiad引用论文Differential dataflow)
(Naiad#引用#GraphX#论文Naiad引用论文GraphX)
(Neo4j#引用#Graph Database#数据库Neo4j引用数据库Graph Database)
(NXgraph#引用#GBASE#论文NXgraph引用论文GBASE)
(NXgraph#引用#GridGraph#论文NXgraph引用论文GridGraph)
(NXgraph#引用#HipG#论文NXgraph引用论文HipG)
(NXgraph#引用#MMap#论文NXgraph引用论文MMap)
(PGAbB#引用#GBASE#论文PGAbB引用论文GBASE)
(PGAbB#引用#Garaph#论文PGAbB引用论文Garaph)
(PGX.D#引用#Distributed GraphLab#论文PGX.D引用论文Distributed GraphLab)
(PGX.D#引用#GraphX#论文PGX.D引用论文GraphX)
(PGX.D#引用#STINGER#论文PGX.D引用论文STINGER)
(PartitionedVC#引用#GraphMP(1)#论文PartitionedVC引用论文GraphMP(1)
(PartitionedVC#引用#Lumos#论文PartitionedVC引用论文Lumos)
(PathGraph#引用#Medusa#论文PathGraph引用论文Medusa)
(Pimiendst#引用#FOG#论文Pimiendst引用论文FOG)
(Pimiendst#引用#Gemini#论文Pimiendst引用论文Gemini)
(Pimiendst#引用#NXgraph#论文Pimiendst引用论文NXgraph)
(Pimiendst#引用#VENUS#论文Pimiendst引用论文VENUS)
(PowerLyra#引用#Blogel#论文PowerLyra引用论文Blogel)
(PowerLyra#引用#Chronos#论文PowerLyra引用论文Chronos)
(PowerLyra#引用#GraphBuilder#论文PowerLyra引用论文GraphBuilder)
(PowerLyra#引用#LFGraph#论文PowerLyra引用论文LFGraph)
(PowerLyra#引用#Mizan#论文PowerLyra引用论文Mizan)
(PowerLyra#引用#X-Stream#论文PowerLyra引用论文X-Stream)
(PrIter#引用#GraphLab#论文PrIter引用论文GraphLab)
(Pregelix#引用#Trinity#论文Pregelix引用论文Trinity)
(Quegel#引用#Blogel#论文Quegel引用论文Blogel)
(Quegel#引用#GraphX#论文Quegel引用论文GraphX)
(Quegel#引用#Trinity#论文Quegel引用论文Trinity)

### 8.8 论文图谱子图-8
Entities:
(Raphdstry#Streaming Analysis Of Distributed Temporal Graphs)
(GraphTides#A Framework for Evaluating Stream-based Graph Processing Platforms)
(ReGraph#A Graph Processing Framework that Alternately Shrinks and Repartitions the Graph)
(Gemini#A Computation-Centric Distributed Graph Processing System)
(GoFFish#A Sub-graph Centric Framework for Large-Scale Graph Analytics)
(HitGraph#High-throughput Graph Processing Framework on FPGA)
(Ringo#Interactive Graph Analytics on Big-Memory Machines)
(TurboGraph#A Fast Parallel Graph Engine Handling Billion-scale Graphs in a Single PC)
(RisGraph#A Real-Time Streaming System for Evolving Graphs dst Support Sub-millisecond Per-update Analysis at Millions Ops/s)
(GraphOne#A Data Sdstre for Real-time Analytics on Evolving Graphs)
(ImmortalGraph#A System for Sdstrage and Analysis of Temporal Graphs)
(Lumos#Dependency-Driven Disk-based Graph Processing)
(SGraph#A Distributed Streaming System for Processing Big Graphs)
(Giraph#DB)
(GraphBuilder#Scalable Graph ETL Framework)
(SaGraph#A Similarity-aware Hardware Acceleradstr for Temporal Graph Processing)
(EGraph#Efficient concurrent GPU-based dynamic graph processing)
(ScalaGraph#A Scalable Acceleradstr for Massively Parallel Graph Processing)
(GraphABCD#Scaling Out Graph Analytics with Asynchronous Block Coordinate Descent)
(GraphPulse#An Event-Driven Hardware Acceleradstr for Asynchronous Graph Processing)
(Scaph#Scalable GPU-Accelerated Graph Processing with Value-Driven Differential Scheduling)
(Subway#Minimizing Data Transfer during Out-of-GPU-Memory Graph Processing)
(ThunderGP#HLS-based graph processing framework on FPGAs)
(ScaleG#A Distributed Disk-based System for Vertex-centric Graph Processing)
(GraphD#Distributed Vertex-Centric Graph Processing Beyond the Memory Limit)
(HGraph#I/O-efficient Distributed and Iterative Graph Computing by Hybrid Pushing/Pulling)
(Medusa#Simplified Graph Processing on GPUs)
(NXgraph#An Efficient Graph Processing System on a Single Machine*)
(GraphPhi#Efficient Parallel Graph Processing on Emerging Throughput-oriented Architectures)
(Seraph#an Efficient, Low-cost System for Concurrent Graph Processing)
(Trinity#A Distributed Graph Engine on a Memory Cloud)
(ShenTu#Processing Multi-Trillion Edge Graphs on Millions of Cores in Seconds)
(Graphene#Fine-Grained IO Management for Graph Computing)
(MOSAIC#Processing a Trillion-Edge Graph on a Single Machine)
(Garaph#Efficient GPU-accelerated Graph Processing on a Single Machine with Balanced Replication)
(Graphie#Large-Scale Asynchronous Graph Traversals on Just a GPU)
(SympleGraph#Distributed Graph Processing with Precise Loop-Carried Dependency Guarantee)
(TDGraph#A dstpology-Driven Acceleradstr for High-Performance Streaming Graph Processing)
(DepGraph#A Dependency-Driven Acceleradstr for Efficient Iterative Graph Processing)
(TIDE#Dynamic Interaction Graphs with Probabilistic Edge Decay)
(GraphX#A Resilient Distributed Graph System on Spark)
(TeGraph#A Novel General-Purpose Temporal Graph Computing Engine)
(Auxo#A Temporal Graph Management System)
(DynamoGraph#A Distributed System for Large-scale, Temporal Graph Processing, its Implementation and First Observations)
(LiveGraph#A Transactional Graph Sdstrage System with Purely Sequential Adjacency List Scans)
(TeGraph+#Scalable Temporal Graph Processing Enabling Flexible Edge Modifications)
(Blaze#Fast Graph Processing on Fast SSDs)
(CommonGraph#Graph Analytics on Evolving Data)
(GraphScope#A Unified Engine For Big Graph Processing)
(Graphflow#An Active Graph Database)
(Tegra#Efficient Ad-Hoc Analytics on Evolving Graphs)
(GraphP#Reducing Communication for PIM-based Graph Processing with Efficient Data Partition)
(Wonderland#A Novel Abstraction-Based Out-Of-Core Graph Processing System)
(Chaos#Scale-out Graph Processing src Secondary Sdstrage)
(ForeGraph#Exploring Large-scale Graph Processing on Multi-FPGA Architecture)
(TigerGraph#DB)
(JanusGraph#DB)
(PowerGraph#Distributed Graph-Parallel Computation on Natural Graphs)
(Tigr#Transforming Irregular Graphs for GPU-Friendly Graph Processing)
(GraphReduce#Processing Large-Scale Graphs on Acceleradstr-Based Systems)
(dstrnado#A System For Real-Time Iterative Analysis Over Evolving Data)
(ASPIRE#Exploiting Asynchronous Parallelism in Iterative Algorithms using a Relaxed Consistency based DSM)
(Naiad#A Timely Dataflow System)

Relationships:
(Raphdstry#引用#GraphTides#论文Raphdstry引用论文GraphTides)
(ReGraph#引用#Gemini#论文ReGraph引用论文Gemini)
(ReGraph#引用#GoFFish#论文ReGraph引用论文GoFFish)
(ReGraph#引用#HitGraph#论文ReGraph引用论文HitGraph)
(Ringo#引用#TurboGraph#论文Ringo引用论文TurboGraph)
(RisGraph#引用#GraphOne#论文RisGraph引用论文GraphOne)
(RisGraph#引用#ImmortalGraph#论文RisGraph引用论文ImmortalGraph)
(RisGraph#引用#Lumos#论文RisGraph引用论文Lumos)
(SGraph#引用#Giraph#论文SGraph引用数据库Giraph)
(SGraph#引用#GraphBuilder#论文SGraph引用论文GraphBuilder)
(SaGraph#引用#EGraph#论文SaGraph引用论文EGraph)
(SaGraph#引用#ScalaGraph#论文SaGraph引用论文ScalaGraph)
(ScalaGraph#引用#GraphABCD#论文ScalaGraph引用论文GraphABCD)
(ScalaGraph#引用#GraphPulse#论文ScalaGraph引用论文GraphPulse)
(ScalaGraph#引用#Scaph#论文ScalaGraph引用论文Scaph)
(ScalaGraph#引用#Subway#论文ScalaGraph引用论文Subway)
(ScalaGraph#引用#ThunderGP#论文ScalaGraph引用论文ThunderGP)
(Scaph#引用#GraphPhi#论文Scaph引用论文GraphPhi)
(Scaph#引用#Lumos#论文Scaph引用论文Lumos)
(Subway#引用#Garaph#论文Subway引用论文Garaph)
(Subway#引用#Graphie#论文Subway引用论文Graphie)
(ThunderGP#引用#Chaos#论文ThunderGP引用论文Chaos)
(ThunderGP#引用#ForeGraph#论文ThunderGP引用论文ForeGraph)
(ScaleG#引用#GraphD#论文ScaleG引用论文GraphD)
(ScaleG#引用#HGraph#论文ScaleG引用论文HGraph)
(ScaleG#引用#Medusa#论文ScaleG引用论文Medusa)
(ScaleG#引用#NXgraph#论文ScaleG引用论文NXgraph)
(Seraph#引用#Trinity#论文Seraph引用论文Trinity)
(ShenTu#引用#Gemini#论文ShenTu引用论文Gemini)
(ShenTu#引用#Graphene#论文ShenTu引用论文Graphene)
(ShenTu#引用#MOSAIC#论文ShenTu引用论文MOSAIC)
(SympleGraph#引用#Lumos#论文SympleGraph引用论文Lumos)
(TDGraph#引用#DepGraph#论文TDGraph引用论文DepGraph)
(TIDE#引用#GraphX#论文TIDE引用论文GraphX)
(TIDE#引用#Trinity#论文TIDE引用论文Trinity)
(TeGraph#引用#Auxo#论文TeGraph引用论文Auxo)
(TeGraph#引用#DynamoGraph#论文TeGraph引用论文DynamoGraph)
(TeGraph#引用#GraphTides#论文TeGraph引用论文GraphTides)
(TeGraph#引用#LiveGraph#论文TeGraph引用论文LiveGraph)
(TeGraph#引用#Lumos#论文TeGraph引用论文Lumos)
(TeGraph+#引用#Blaze#论文TeGraph+引用论文Blaze)
(TeGraph+#引用#CommonGraph#论文TeGraph+引用论文CommonGraph)
(TeGraph+#引用#GraphScope#论文TeGraph+引用论文GraphScope)
(TeGraph+#引用#Graphflow#论文TeGraph+引用论文Graphflow)
(TeGraph+#引用#SaGraph#论文TeGraph+引用论文SaGraph)
(TeGraph+#引用#TeGraph#论文TeGraph+引用论文TeGraph)
(Tegra#引用#GraphOne#论文Tegra引用论文GraphOne)
(Tegra#引用#GraphP#论文Tegra引用论文GraphP)
(Tegra#引用#ImmortalGraph#论文Tegra引用论文ImmortalGraph)
(Tegra#引用#Wonderland#论文Tegra引用论文Wonderland)
(TigerGraph#引用#Giraph#数据库TigerGraph引用数据库Giraph)
(TigerGraph#引用#JanusGraph#数据库TigerGraph引用数据库JanusGraph)
(TigerGraph#引用#PowerGraph#数据库TigerGraph引用论文PowerGraph)
(Tigr#引用#Giraph#论文Tigr引用数据库Giraph)
(Tigr#引用#GraphReduce#论文Tigr引用论文GraphReduce)
(dstrnado#引用#ASPIRE#论文dstrnado引用论文ASPIRE)
(dstrnado#引用#Naiad#论文dstrnado引用论文Naiad)

### 8.9 论文图谱子图-9
Entities:
(dstrnado#A System For Real-Time Iterative Analysis Over Evolving Data)
(Trinity#A Distributed Graph Engine on a Memory Cloud)
(Giraph#DB)
(GraphChi#Large-Scale Graph Computation on Just a PC)
(Tripoline#Generalized Incremental Graph Processing via Graph Triangle Inequality)
(Chaos#Scale-out Graph Processing src Secondary Sdstrage)
(FENNEL#Streaming Graph Partitioning for Massive Scale Graphs)
(GRAPE#Parallelizing Sequential Graph Computations)
(Subway#Minimizing Data Transfer during Out-of-GPU-Memory Graph Processing)
(Tigr#Transforming Irregular Graphs for GPU-Friendly Graph Processing)
(TuGraph#DB)
(JanusGraph#DB)
(Neo4j#DB)
(TurboGraph#A Fast Parallel Graph Engine Handling Billion-scale Graphs in a Single PC)
(VENUS#A System for Streamlined Graph Computation on a Single PC)
(GasCL#A Vertex-Centric Graph Model for GPUs)
(MOCgraph#Scalable Distributed Graph Processing Using Message Online Computing)
(VGL#a high-performance graph processing framework for the NEC SX-Aurora TSUBASA vecdstr architecture)
(CuSha#Vertex-Centric Graph Processing on GPUs)
(Ligra#A Lightweight Graph Processing Framework for Shared Memory)
(Version Traveler#Fast and Memory-Efficient Version Switching in Graph Processing Systems)
(LFGraph#Simple and Fast Distributed Graph Analytics)
(LLAMA#Efficient Graph Analytics Using Large Multiversioned Arrays.)
(Ringo#Interactive Graph Analytics on Big-Memory Machines)
(X-Stream#Edge-centric Graph Processing using Streaming Partitions)
(VeilGraph#Approximating Graph Streams)
(Weaver#A High-Performance, Transactional Graph Database Based on Refinable Timestamps)
(Blogel#A Block-Centric Framework for Distributed Computation on Real-World Graphs)
(GraphX#A Resilient Distributed Graph System on Spark)
(GridGraph#Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning)
(Pregelix#Big(ger) Graph Analytics on A Dataflow Engine)
(WolfGraph#the Edge-Centric graph processing on GPU)
(Frog#Asynchronous graph processing on GPU with hybrid coloring model)
(Graphie#Large-Scale Asynchronous Graph Traversals on Just a GPU)
(Wonderland#A Novel Abstraction-Based Out-Of-Core Graph Processing System)
(G-Sdstre#High-Performance Graph Sdstre for Trillion-Edge Processing)
(Gemini#A Computation-Centric Distributed Graph Processing System)
(KickStarter#Fast and Accurate Computations on Streaming Graphs via Trimmed Approximations)
(XPGraph#XPline-Friendly Persistent Memory Graph Sdstres for Large-Scale Evolving Graphs)
(GraphIn#An Online High Performance Incremental Graph Processing Framework)
(GraphOne#A Data Sdstre for Real-time Analytics on Evolving Graphs)
(Graphene#Fine-Grained IO Management for Graph Computing)
(LiveGraph#A Transactional Graph Sdstrage System with Purely Sequential Adjacency List Scans)
(Lumos#Dependency-Driven Disk-based Graph Processing)
(faimGraph#High Performance Management of Fully-Dynamic Graphs under tight Memory Constraints on the GPU)
(Zorro#Zero-Cost Reactive Failure Recovery in Distributed Graph Processing)
(iGraph#an incremental data processing system for dynamic graph)
(iMapReduce#A Distributed Computing Framework for Iterative Computation)
(HaLoop#Efficient Iterative Data Processing on Large Clusters)
(PrIter#A Distributed Framework for Prioritized Iterative Computations)
(iPregel#A Combiner-Based In-Memory Shared Memory Vertex-Centric Framework)
(GraphD#Distributed Vertex-Centric Graph Processing Beyond the Memory Limit)
(iTurboGraph#Scaling and Audstmating Incremental Graph Analytics)
(xDGP#A Dynamic Graph Processing System with Adaptive Partitioning)
(Mizan#A System for Dynamic Load Balancing in Large-scale Graph Processing)

Relationships:
(dstrnado#引用#Trinity#论文dstrnado引用论文Trinity)
(Trinity#引用#Giraph#论文Trinity引用数据库Giraph)
(Trinity#引用#GraphChi#论文Trinity引用论文GraphChi)
(Tripoline#引用#Chaos#论文Tripoline引用论文Chaos)
(Tripoline#引用#FENNEL#论文Tripoline引用论文FENNEL)
(Tripoline#引用#GRAPE#论文Tripoline引用论文GRAPE)
(Tripoline#引用#Subway#论文Tripoline引用论文Subway)
(Tripoline#引用#Tigr#论文Tripoline引用论文Tigr)
(TuGraph#引用#JanusGraph#数据库TuGraph引用数据库JanusGraph)
(TuGraph#引用#Neo4j#数据库TuGraph引用数据库Neo4j)
(TurboGraph#引用#Trinity#论文TurboGraph引用论文Trinity)
(VENUS#引用#GasCL#论文VENUS引用论文GasCL)
(VENUS#引用#MOCgraph#论文VENUS引用论文MOCgraph)
(VGL#引用#CuSha#论文VGL引用论文CuSha)
(VGL#引用#Ligra#论文VGL引用论文Ligra)
(Version Traveler#引用#LFGraph#论文Version Traveler引用论文LFGraph)
(Version Traveler#引用#LLAMA#论文Version Traveler引用论文LLAMA)
(Version Traveler#引用#MOCgraph#论文Version Traveler引用论文MOCgraph)
(Version Traveler#引用#Ringo#论文Version Traveler引用论文Ringo)
(Version Traveler#引用#X-Stream#论文Version Traveler引用论文X-Stream)
(X-Stream#引用#GraphChi#论文X-Stream引用论文GraphChi)
(VeilGraph#引用#dstrnado#论文VeilGraph引用论文dstrnado)
(Weaver#引用#Blogel#论文Weaver引用论文Blogel)
(Weaver#引用#GraphX#论文Weaver引用论文GraphX)
(Weaver#引用#GridGraph#论文Weaver引用论文GridGraph)
(Weaver#引用#MOCgraph#论文Weaver引用论文MOCgraph)
(Weaver#引用#Pregelix#论文Weaver引用论文Pregelix)
(WolfGraph#引用#Frog#论文WolfGraph引用论文Frog)
(WolfGraph#引用#Graphie#论文WolfGraph引用论文Graphie)
(WolfGraph#引用#Tigr#论文WolfGraph引用论文Tigr)
(WolfGraph#引用#Wonderland#论文WolfGraph引用论文Wonderland)
(Wonderland#引用#G-Sdstre#论文Wonderland引用论文G-Sdstre)
(Wonderland#引用#Gemini#论文Wonderland引用论文Gemini)
(Wonderland#引用#KickStarter#论文Wonderland引用论文KickStarter)
(XPGraph#引用#FENNEL#论文XPGraph引用论文FENNEL)
(XPGraph#引用#GraphIn#论文XPGraph引用论文GraphIn)
(XPGraph#引用#GraphOne#论文XPGraph引用论文GraphOne)
(XPGraph#引用#Graphene#论文XPGraph引用论文Graphene)
(XPGraph#引用#LiveGraph#论文XPGraph引用论文LiveGraph)
(XPGraph#引用#Lumos#论文XPGraph引用论文Lumos)
(XPGraph#引用#faimGraph#论文XPGraph引用论文faimGraph)
(faimGraph#引用#GasCL#论文faimGraph引用论文GasCL)
(Zorro#引用#LFGraph#论文Zorro引用论文LFGraph)
(Zorro#引用#LLAMA#论文Zorro引用论文LLAMA)
(Zorro#引用#X-Stream#论文Zorro引用论文X-Stream)
(iGraph#引用#Blogel#论文iGraph引用论文Blogel)
(iGraph#引用#X-Stream#论文iGraph引用论文X-Stream)
(iMapReduce#引用#HaLoop#论文iMapReduce引用论文HaLoop)
(iMapReduce#引用#PrIter#论文iMapReduce引用论文PrIter)
(iPregel#引用#GraphD#论文iPregel引用论文GraphD)
(iTurboGraph#引用#GraphD#论文iTurboGraph引用论文GraphD)
(iTurboGraph#引用#GraphOne#论文iTurboGraph引用论文GraphOne)
(xDGP#引用#Mizan#论文xDGP引用论文Mizan)