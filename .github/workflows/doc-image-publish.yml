name: Build and push docs image
on:
  push:
    branches:
      - main
    paths:
      - 'docs/**'
      - '.github/workflows/doc-image-publish.yml'
  pull_request:
    paths:
      - 'docs/**'
      - '.github/workflows/doc-image-publish.yml'
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to build (default: main)'
        required: true
        default: 'main'
        type: string
permissions:
  contents: read
jobs:
  build-image:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0 
          ref: ${{ github.event_name == 'workflow_dispatch' && inputs.branch || github.ref }}
      
      - name: Debug git status 
        run: |
            echo "Current commit: $(git rev-parse HEAD)"
            echo "Current branch: $(git rev-parse --abbrev-ref HEAD || echo 'detached HEAD')"
            echo "Main branch commit: $(git rev-parse origin/main)"
            echo "Using branch: ${{ github.event_name == 'workflow_dispatch' && inputs.branch || github.ref_name }}"
      
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        
      # Only login to Docker Hub when not in PR
      - name: Login to Docker Hub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
          
      # Only run when there is a PR
      - name: Build PR
        if: github.event_name == 'pull_request'
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./docs/Dockerfile-deploy
          platforms: linux/amd64
          push: false
      
      # Only run when it is not a PR, build and push the image
      - name: Build and push
        if: github.event_name != 'pull_request'
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./docs/Dockerfile-deploy
          platforms: linux/amd64
          push: true
          tags: eosphorosai/dbgpt-docs:${{ github.event_name == 'workflow_dispatch' && inputs.branch || github.ref_name }},eosphorosai/dbgpt-docs:latest