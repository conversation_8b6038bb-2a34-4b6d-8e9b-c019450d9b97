name: Python Code Quality Checks

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

concurrency:
  group: ${{ github.event.number || github.run_id }}
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Install uv
      run: curl -LsSf https://astral.sh/uv/install.sh | sh
    - name: Install dependencies and setup environment
      run: |
        uv -V
        make setup
    - name: Check Python code style
      run: make fmt-check
    # TODO: Add mypy check
    # - name: Check Python code type
    #   run: make mypy
