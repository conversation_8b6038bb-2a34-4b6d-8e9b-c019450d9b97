
name: <PERSON>uild and Push OpenAI Docker Image

on:
  release:
    types: [published]
  workflow_dispatch: 

permissions:
  contents: read

jobs:
  build-image:
    runs-on: ubuntu-latest
    # run unless event type is pull_request
    if: github.event_name != 'pull_request'
    steps:
      - uses: actions/checkout@v3
      - name: free disk space
        run: |
          sudo swapoff -a
          sudo rm -f /swapfile
          sudo apt clean
          # Only delete Docker images when they exist
          if [ ! -z "$(docker image ls -aq)" ]; then
            docker rmi $(docker image ls -aq)
          fi
          df -h
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./docker/base/Dockerfile
          build-args: |
            BASE_IMAGE=ubuntu:22.04
            EXTRAS=base,proxy_openai,rag,graph_rag,storage_chromadb,dbgpts,proxy_ollama,proxy_zhipuai,proxy_anthropic,proxy_qianfan,proxy_tongyi
            PIP_INDEX_URL=https://pypi.org/simple
          platforms: linux/arm64,linux/amd64
          push: true
          tags: eosphorosai/dbgpt-openai:${{ github.ref_name }},eosphorosai/dbgpt-openai:latest