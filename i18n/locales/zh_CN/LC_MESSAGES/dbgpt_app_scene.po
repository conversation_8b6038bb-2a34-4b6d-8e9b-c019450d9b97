# Chinese translations for PACKAGE package
# PACKAGE 软件包的简体中文翻译.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-03-19 00:06+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_knowledge/v1/config.py:21
msgid "The number of chunks to retrieve from the knowledge space."
msgstr "从知识空间中检索的块数。"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_knowledge/v1/config.py:26
msgid "The number of chunks after reranking."
msgstr "重新排序后的块数。"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_knowledge/v1/config.py:30
msgid "The minimum similarity score to return from the query."
msgstr "查询返回的最小相似度得分。"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_knowledge/v1/config.py:36
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_db/professional_qa/config.py:39
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_db/auto_execute/config.py:39
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_normal/config.py:22
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_data/chat_excel/config.py:44
msgid "Memory configuration"
msgstr "内存配置"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_db/professional_qa/config.py:20
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_db/auto_execute/config.py:20
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_dashboard/config.py:15
msgid "The number of tables to retrieve from the database."
msgstr "从数据库中检索的表的数量。"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_db/professional_qa/config.py:26
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_db/auto_execute/config.py:26
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_dashboard/config.py:21
msgid ""
"The maximum number of tokens to pass to the model, default 100 * 1024.Just "
"work for the schema retrieval failed, and load all tables schema."
msgstr "传递给模型的最大 Token 数，默认为 100 * 1024。仅在模式检索失败并加载所有表模式时生效。"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_db/professional_qa/config.py:33
#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_db/auto_execute/config.py:33
msgid "The maximum number of results to return from the query."
msgstr "查询返回的最大结果数。"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_data/chat_excel/config.py:22
msgid ""
"The directory of the duckdb extensions.Duckdb will download the extensions "
"from the internet if not provided.This configuration is used to tell duckdb "
"where to find the extensions and avoid downloading. Note that the extensions "
"are platform-specific and version-specific."
msgstr "DuckDB 扩展的目录。如果未提供，DuckDB 将从互联网下载扩展。此配置用于告知 DuckDB 在哪里找到扩展，以避免下载。请注意，扩展是特定于平台和版本的。"

#: ../packages/dbgpt-app/src/dbgpt_app/scene/chat_data/chat_excel/config.py:34
msgid ""
"Whether to force install the duckdb extensions. If True, the extensions will "
"be installed even if they are already installed."
msgstr "是否强制安装 DuckDB 扩展。如果为 True，则即使扩展已经安装，也会重新安装。"