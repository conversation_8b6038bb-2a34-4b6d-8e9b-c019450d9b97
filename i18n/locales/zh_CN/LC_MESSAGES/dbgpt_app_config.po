# Chinese translations for PACKAGE package
# PACKAGE 软件包的简体中文翻译.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 07:51+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:30
msgid "Language setting"
msgstr "语言设置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:37
msgid "Logging level"
msgstr "日志级别"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:44
msgid "API keys"
msgstr "API 密钥"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:49
msgid "The key to encrypt the data"
msgstr "用于加密数据的密钥"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:60
msgid "default vector type"
msgstr "默认向量类型"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:66
msgid "default graph type"
msgstr "默认图类型"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:85
#: ../packages/dbgpt-app/src/dbgpt_app/config.py:270
msgid "Whether to verify the SSL certificate of the database"
msgstr "是否验证数据库的 SSL 证书"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:91
#: ../packages/dbgpt-app/src/dbgpt_app/config.py:276
msgid ""
"The default thread pool size, If None, use default config of python thread "
"pool"
msgstr "默认线程池大小，如果为 None，则使用 Python 线程池的默认配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:98
msgid "knowledge search top k"
msgstr "知识搜索前 K 个结果"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:102
msgid "knowledge search top similarity score"
msgstr "知识搜索最高相似度分数"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:106
msgid "knowledge search rewrite"
msgstr "知识搜索重写"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:110
msgid "knowledge max chunks once load"
msgstr "知识单次加载的最大分块数"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:114
msgid "knowledge max load thread"
msgstr "知识加载的最大线程数"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:118
msgid "knowledge rerank top k"
msgstr "知识重排序前 K 个结果"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:122
msgid "Storage configuration"
msgstr "存储配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:126
msgid "knowledge graph search top k"
msgstr "知识图谱搜索前 K 个结果"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:130
msgid "graph community summary enabled"
msgstr "启用图社区摘要"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:134
msgid "kg extract llm model"
msgstr "知识图谱提取大语言模型"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:142
msgid "kg extract score threshold"
msgstr "知识图谱提取分数阈值"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:146
msgid "kg community top k"
msgstr "知识图谱社区前 K 个结果"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:150
msgid "kg_community_score_threshold"
msgstr "知识图谱社区分数阈值"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:154
msgid "kg_triplet_graph_enabled"
msgstr "启用知识图谱三元组图"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:158
msgid "kg_document_graph_enabled"
msgstr "启用知识图谱文档图"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:162
msgid "kg_chunk_search_top_k"
msgstr "知识图谱分块搜索前 K 个结果"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:166
msgid "kg_extraction_batch_size"
msgstr "知识图谱提取批量大小"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:174
msgid "kg_embedding_batch_size"
msgstr "知识图谱嵌入批量大小"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:178
msgid "kg_similarity_top_k"
msgstr "知识图谱相似度前 k 个结果"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:182
msgid "kg_similarity_score_threshold"
msgstr "知识图谱相似度分数阈值"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:186
msgid "kg_enable_text_search"
msgstr "启用知识图谱文本搜索"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:190
msgid "kg_text2gql_model_enabled"
msgstr "知识图谱文本转图查询语言模型是否启用"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:194
msgid "text2gql_model_name"
msgstr "文本转图查询语言模型名称"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:198
msgid "bm25_k1"
msgstr "BM25 算法的 k1 参数"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:202
msgid "bm25_b"
msgstr "BM25 算法的 b 参数"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:209
msgid "Webserver deploy host"
msgstr "Web 服务器部署主机"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:211
msgid "Webserver deploy port, default is 5670"
msgstr "Web 服务器部署端口，默认为 5670"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:214
msgid "Run Webserver in light mode"
msgstr "以轻量模式运行 Web 服务器"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:220
msgid ""
"The Model controller address to connect. If None, read model controller "
"address from environment key `MODEL_SERVER`."
msgstr ""
"要连接的模型控制器地址。若为空，则从环境变量 `MODEL_SERVER` 中读取模型控制器地址。"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:231
msgid "Database connection config, now support SQLite, OceanBase and MySQL"
msgstr "数据库连接配置，目前支持 SQLite、OceanBase 和 MySQL"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:239
msgid ""
"The storage type of model configures, if None, use the default "
"storage(current database). When you run in light mode, it will not use any "
"storage."
msgstr ""
"模型配置的存储类型，若为空，则使用默认存储（当前数据库）。在轻量模式下运行时，不会使用任何存储。"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:249
msgid "Tracer config for web server, if None, use global tracer config"
msgstr "Web 服务器的追踪器配置，若为空，则使用全局追踪器配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:256
msgid "Logging configuration for web server, if None, use global config"
msgstr "Web 服务器的日志配置，若为空，则使用全局配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:264
msgid "Whether to disable alembic to initialize and upgrade database metadata"
msgstr "是否禁用 Alembic 初始化和升级数据库元数据"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:285
msgid ""
"Whether to enable remote embedding models. If it is True, you need to start "
"a embedding model through `dbgpt start worker --worker_type text2vec --"
"model_name xxx --model_path xxx`"
msgstr ""
"是否启用远程嵌入模型。若为 True，则需通过 `dbgpt start worker --worker_type text2vec --model_name xxx --model_path xxx` 启动嵌入模型"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:295
msgid ""
"Whether to enable remote rerank models. If it is True, you need to start a "
"rerank model through `dbgpt start worker --worker_type text2vec --rerank --"
"model_name xxx --model_path xxx`"
msgstr ""
"是否启用远程重排序模型。如果为 True，则需要通过 `dbgpt start worker --worker_type text2vec --rerank --model_name xxx --model_path xxx` 启动重排序模型"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:303
msgid "The directories to search awel files, split by `,`"
msgstr "用于搜索 AWEL 文件的目录，用逗号分隔"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:307
msgid "Whether to use the new web UI, default is True"
msgstr "是否使用新的 Web UI，默认为 True"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:311
msgid "Model cache configuration"
msgstr "模型缓存配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:316
msgid "The max sequence length of the embedding model, default is 512"
msgstr "嵌入模型的最大序列长度，默认为 512"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:327
msgid "Web service configuration"
msgstr "Web 服务配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:331
msgid "Model service configuration"
msgstr "模型服务配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:343
msgid ""
"Configuration hooks, which will be executed before the configuration loading"
msgstr "配置钩子，在加载配置之前执行"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:352
msgid "System configuration"
msgstr "系统配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:359
msgid "Model deployment configuration"
msgstr "模型部署配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:365
msgid "Serve configuration"
msgstr "服务配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:370
msgid "Rag Knowledge Parameters"
msgstr "RAG 知识参数"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:379
msgid "Global tracer configuration"
msgstr "全局跟踪器配置"

#: ../packages/dbgpt-app/src/dbgpt_app/config.py:385
msgid "Logging configuration"
msgstr "日志记录配置"