# French translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:24
msgid "File Serve Configurations"
msgstr "Configurations du service de fichiers"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:28
msgid ""
"This configuration is for the file serve module. In DB-GPT, you can store "
"yourfiles in the file server."
msgstr "Cette configuration est destinée au module de service de fichiers. Dans DB-GPT, vous pouvez stocker vos fichiers sur le serveur de fichiers."

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:49
msgid "Check the hash of the file when downloading"
msgstr "Vérifier le hachage du fichier lors du téléchargement"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:52
msgid "The host of the file server"
msgstr "L'hôte du serveur de fichiers"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:56
msgid "The port of the file server, default is 5670"
msgstr "Le port du serveur de fichiers, la valeur par défaut est 5670"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:60
msgid "The chunk size when downloading the file"
msgstr "La taille des blocs lors du téléchargement du fichier"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:63
msgid "The chunk size when saving the file"
msgstr "La taille des blocs lors de la sauvegarde du fichier"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:67
msgid "The chunk size when transferring the file"
msgstr "La taille des blocs lors du transfert du fichier"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:70
msgid "The timeout when transferring the file"
msgstr "Le délai d'attente lors du transfert du fichier"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:73
msgid "The local storage path"
msgstr "Le chemin de stockage local"

#: ../packages/dbgpt-serve/src/dbgpt_serve/file/config.py:81
msgid "The storage backend configurations"
msgstr "Les configurations du backend de stockage"