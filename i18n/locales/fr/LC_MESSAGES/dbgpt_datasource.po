# French translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:57
msgid "Database host, e.g., localhost"
msgstr "Hôte de la base de données, par exemple, localhost"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:58
msgid "Database port, e.g., 3306"
msgstr "Port de la base de données, par exemple, 3306"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:59
msgid "Database user to connect"
msgstr "Utilisateur de la base de données pour la connexion"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:60
msgid "Database name"
msgstr "Nom de la base de données"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:61
msgid "Database driver, e.g., mysql+pymysql"
msgstr "Pilote de la base de données, par exemple, mysql+pymysql"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:66
msgid ""
"Database password, you can write your password directly, of course, you can "
"also use environment variables, such as ${env:DBGPT_DB_PASSWORD}"
msgstr ""
"Mot de passe de la base de données, vous pouvez écrire votre mot de passe "
"directement, bien sûr, vous pouvez également utiliser des variables "
"d'environnement, comme ${env:DBGPT_DB_PASSWORD}"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:75
msgid "Connection pool size, default 5"
msgstr "Taille du pool de connexions, par défaut 5"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:78
msgid "Max overflow connections, default 10"
msgstr "Nombre maximum de connexions débordantes, par défaut 10"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:81
msgid "Connection pool timeout, default 30"
msgstr "Délai d'attente du pool de connexions, par défaut 30"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:84
msgid "Connection pool recycle, default 3600"
msgstr "Recyclage du pool de connexions, par défaut 3600"

#: ../packages/dbgpt-core/src/dbgpt/datasource/rdbms/base.py:87
msgid "Connection pool pre ping, default True"
msgstr "Pré-ping du pool de connexions, par défaut True"