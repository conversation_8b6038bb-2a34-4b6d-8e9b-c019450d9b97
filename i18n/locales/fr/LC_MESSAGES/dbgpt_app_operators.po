# French translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:35
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:145
msgid "Context Key"
msgstr "Clé de contexte"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:40
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:150
msgid "The key of the context, it will be used in building the prompt"
msgstr "La clé du contexte, elle sera utilisée pour construire l'invite"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:43
msgid "Top K"
msgstr "Top K"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:48
msgid "The number of chunks to retrieve"
msgstr "Le nombre de segments à récupérer"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:51
msgid "Minimum Match Score"
msgstr "Score de correspondance minimum"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:58
msgid ""
"The minimum match score for the retrieved chunks, it will be dropped if the "
"match score is less than the threshold"
msgstr ""
"Le score de correspondance minimum pour les segments récupérés, il sera "
"écarté si le score de correspondance est inférieur au seuil"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:66
msgid "Reranker Enabled"
msgstr "Reranker activé"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:71
msgid "Whether to enable the reranker"
msgstr "Indique si le reranker doit être activé"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:74
msgid "Reranker Top K"
msgstr "Top K du reranker"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:79
msgid "The top k for the reranker"
msgstr "Le top k pour le reranker"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:83
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:153
msgid "User question"
msgstr "Question de l'utilisateur"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:86
msgid "The user question to retrieve the knowledge"
msgstr "La question de l'utilisateur pour récupérer les connaissances"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:89
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:159
msgid "Retrieved context"
msgstr "Contexte récupéré"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:92
msgid "The retrieved context from the knowledge space"
msgstr "Le contexte récupéré de l'espace de connaissances"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:107
msgid "Knowledge Space Operator"
msgstr "Opérateur d'espace de connaissances"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:111
msgid "Knowledge Space Operator, retrieve your knowledge from knowledge space"
msgstr "Opérateur d'espace de connaissances, récupérez vos connaissances de l'espace de connaissances"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:115
msgid "Knowledge Space Name"
msgstr "Nom de l'espace de connaissances"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:119
msgid "The name of the knowledge space"
msgstr "Le nom de l'espace de connaissances"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:133
msgid "Chunks"
msgstr "Morceaux"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:137
msgid "The retrieved chunks from the knowledge space"
msgstr "Les morceaux récupérés de l'espace de connaissances"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:15
#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:40
msgid "String"
msgstr "Chaîne"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:18
msgid "The string to be converted to other types."
msgstr "La chaîne à convertir en d'autres types."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:21
#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:46
msgid "Integer"
msgstr "Entier"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:24
msgid "The integer to be converted to other types."
msgstr "L'entier à convertir en d'autres types."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:27
#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:52
msgid "Float"
msgstr "Flottant"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:30
msgid "The float to be converted to other types."
msgstr "Le flottant à convertir en d'autres types."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:33
#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:58
msgid "Boolean"
msgstr "Booléen"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:36
msgid "The boolean to be converted to other types."
msgstr "Le booléen à convertir en d'autres types."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:43
msgid "The string converted from other types."
msgstr "La chaîne convertie à partir d'autres types."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:49
msgid "The integer converted from other types."
msgstr "L'entier converti à partir d'autres types."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:55
msgid "The float converted from other types."
msgstr "Le flottant converti à partir d'autres types."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:61
msgid "The boolean converted from other types."
msgstr "Le booléen converti à partir d'autres types."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:69
msgid "String to Integer"
msgstr "Chaîne vers Entier"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:71
msgid "Converts a string to an integer."
msgstr "Convertit une chaîne en un entier."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:88
msgid "String to Float"
msgstr "Chaîne vers Flottant"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:90
msgid "Converts a string to a float."
msgstr "Convertit une chaîne en un nombre flottant."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:107
msgid "String to Boolean"
msgstr "Chaîne vers Booléen"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:109
msgid "Converts a string to a boolean, true: 'true', '1', 'y'"
msgstr "Convertit une chaîne en un booléen, vrai : 'true', '1', 'y'"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:113
msgid "True Values"
msgstr "Valeurs Vraies"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:118
msgid "Comma-separated values that should be treated as True."
msgstr "Valeurs séparées par des virgules qui doivent être considérées comme vraies."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:137
msgid "Integer to String"
msgstr "Entier vers Chaîne"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:139
msgid "Converts an integer to a string."
msgstr "Convertit un entier en une chaîne."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:156
msgid "Float to String"
msgstr "Flottant vers Chaîne"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:158
msgid "Converts a float to a string."
msgstr "Convertit un nombre flottant en une chaîne de caractères."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:175
msgid "Boolean to String"
msgstr "Conversion de booléen en chaîne de caractères"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:177
msgid "Converts a boolean to a string."
msgstr "Convertit un booléen en chaîne de caractères."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:194
msgid "Model Output to Dict"
msgstr "Conversion de la sortie du modèle en dictionnaire"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:196
msgid "Converts a model output to a dictionary."
msgstr "Convertit une sortie de modèle en dictionnaire."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:199
#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:391
msgid "Model Output"
msgstr "Sortie du modèle"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:200
msgid "Dictionary"
msgstr "Dictionnaire"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:103
msgid "Datasource"
msgstr "Source de données"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:106
msgid "The datasource to retrieve the context"
msgstr "La source de données pour récupérer le contexte"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:109
#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:283
msgid "Prompt Template"
msgstr "Modèle de prompt"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:114
msgid "The prompt template to build a database prompt"
msgstr "Le modèle de prompt pour construire un prompt de base de données"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:118
msgid "Display Type"
msgstr "Type d'affichage"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:123
msgid "The display type for the data"
msgstr "Le type d'affichage pour les données"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:127
msgid "Max Number of Results"
msgstr "Nombre maximum de résultats"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:132
msgid "The maximum number of results to return"
msgstr "Le nombre maximum de résultats à retourner"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:135
msgid "Response Format"
msgstr "Format de réponse"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:140
msgid "The response format, default is a JSON format"
msgstr "Le format de réponse, par défaut un format JSON"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:156
msgid "The user question to retrieve table schemas from the datasource"
msgstr "La question de l'utilisateur pour récupérer les schémas de table à partir de la source de données"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:162
msgid "The retrieved context from the datasource"
msgstr "Le contexte récupéré à partir de la source de données"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:166
msgid "SQL dict"
msgstr "Dictionnaire SQL"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:169
msgid "The SQL to be executed wrapped in a dictionary, generated by LLM"
msgstr "Le SQL à exécuter encapsulé dans un dictionnaire, généré par le LLM"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:172
msgid "SQL result"
msgstr "Résultat SQL"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:175
msgid "The result of the SQL execution"
msgstr "Le résultat de l'exécution du SQL"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:179
msgid "SQL dict list"
msgstr "Liste de dictionnaires SQL"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:183
msgid "The SQL list to be executed wrapped in a dictionary, generated by LLM"
msgstr "La liste SQL à exécuter, encapsulée dans un dictionnaire, générée par le Grand Modèle de Langage"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:212
msgid "Datasource Retriever Operator"
msgstr "Opérateur de récupération des données sources"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:214
msgid "Retrieve the table schemas from the datasource."
msgstr "Récupérer les schémas de table à partir de la source de données."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:228
msgid "Retrieved schema chunks"
msgstr "Morceaux de schéma récupérés"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:232
msgid "The retrieved schema chunks from the datasource"
msgstr "Les morceaux de schéma récupérés à partir de la source de données"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:290
msgid "Datasource Executor Operator"
msgstr "Opérateur d'exécution des données sources"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:292
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:329
msgid "Execute the context from the datasource."
msgstr "Exécuter le contexte provenant de la source de données."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:327
msgid "Datasource Dashboard Operator"
msgstr "Opérateur du tableau de bord des données sources"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:60
msgid "Code Map Operator"
msgstr "Opérateur de carte de code"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:63
msgid ""
"Handle input dictionary with code and return output dictionary after "
"execution."
msgstr ""
"Traiter le dictionnaire d'entrée contenant le code et retourner le "
"dictionnaire de sortie après exécution."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:69
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:211
msgid "Code Editor"
msgstr "Éditeur de code"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:74
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:216
msgid "Please input your code"
msgstr "Veuillez saisir votre code"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:75
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:217
msgid "The code to be executed."
msgstr "Le code à exécuter."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:81
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:223
msgid "Language"
msgstr "Langage"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:86
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:228
msgid "Please select the language"
msgstr "Veuillez sélectionner le langage"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:87
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:229
msgid "The language of the code."
msgstr "Le langage du code."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:97
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:239
msgid "Call Name"
msgstr "Nom d'appel"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:102
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:244
msgid "Please input the call name"
msgstr "Veuillez saisir le nom d'appel"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:103
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:245
msgid "The call name of the function."
msgstr "Le nom d'appel de la fonction."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:108
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:250
msgid "Input Data"
msgstr "Données d'entrée"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:111
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:253
msgid "The input dictionary."
msgstr "Le dictionnaire d'entrée."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:116
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:258
msgid "Output Data"
msgstr "Données de sortie"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:119
msgid "The output dictionary."
msgstr "Le dictionnaire de sortie."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:202
msgid "Code Dict to Model Request Operator"
msgstr "Opérateur de conversion de dictionnaire de code en requête de modèle"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:205
msgid ""
"Handle input dictionary with code and return output ModelRequest after "
"execution."
msgstr ""
"Traite le dictionnaire d'entrée avec du code et retourne une ModelRequest de "
"sortie après exécution."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:261
msgid "The output ModelRequest."
msgstr "La ModelRequest de sortie."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:53
msgid "The context key can be used as the key for formatting prompt."
msgstr "La clé de contexte peut être utilisée comme clé pour formater l'invite."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:57
msgid "The context."
msgstr "Le contexte."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:63
msgid "You are a helpful AI assistant."
msgstr "Vous êtes un assistant IA utile."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:286
msgid "The prompt template for the conversation."
msgstr "Le modèle d'invite pour la conversation."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:291
msgid "Model Name"
msgstr "Nom du modèle"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:296
msgid "The model name."
msgstr "Le nom du modèle."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:300
msgid "LLM Client"
msgstr "Client LLM"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:306
msgid ""
"The LLM Client, how to connect to the LLM model, if not provided, it will "
"use the default client deployed by DB-GPT."
msgstr ""
"Le client LLM, comment se connecter au modèle LLM. Si non fourni, il "
"utilisera le client par défaut déployé par DB-GPT."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:311
msgid "History Message Merge Mode"
msgstr "Mode de fusion des messages historiques"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:322
msgid ""
"The history merge mode, supports 'none', 'window' and 'token'. 'none': no "
"history merge, 'window': merge by conversation window, 'token': merge by "
"token length."
msgstr ""
"Le mode de fusion de l'historique prend en charge 'none', 'window' et "
"'token'. 'none' : pas de fusion de l'historique, 'window' : fusion par "
"fenêtre de conversation, 'token' : fusion par longueur de token."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:329
msgid "User Message Key"
msgstr "Clé du message utilisateur"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:335
msgid "The key of the user message in your prompt, default is 'user_input'."
msgstr "La clé du message de l'utilisateur dans votre invite, la valeur par défaut est 'user_input'."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:339
msgid "History Key"
msgstr "Clé de l'historique"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:345
msgid ""
"The chat history key, with chat history message pass to prompt template, if "
"not provided, it will parse the prompt template to get the key."
msgstr ""
"La clé de l'historique de chat, avec le message de l'historique de chat transmis au modèle de prompt. Si elle n'est pas fournie, le modèle de prompt sera analysé pour obtenir la clé."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:350
msgid "Keep Start Rounds"
msgstr "Conserver les tours initiaux"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:355
msgid "The start rounds to keep in the chat history."
msgstr "Les tours initiaux à conserver dans l'historique de chat."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:358
msgid "Keep End Rounds"
msgstr "Conserver les tours finaux"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:363
msgid "The end rounds to keep in the chat history."
msgstr "Les tours finaux à conserver dans l'historique de chat."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:366
msgid "Max Token Limit"
msgstr "Limite maximale de tokens"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:371
msgid "The max token limit to keep in the chat history."
msgstr "La limite maximale de tokens à conserver dans l'historique de chat."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:375
msgid "Common LLM Request Body"
msgstr "Corps de requête LLM commun"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:378
msgid "The common LLM request body."
msgstr "Le corps de requête LLM commun."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:381
msgid "Extra Context"
msgstr "Contexte supplémentaire"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:385
msgid ""
"Extra context for building prompt(Knowledge context, database schema, etc), "
"you can add multiple context."
msgstr ""
"Contexte supplémentaire pour la construction de l'invite (contexte de "
"connaissances, schéma de base de données, etc.), vous pouvez ajouter "
"plusieurs contextes."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:394
msgid "The model output."
msgstr "La sortie du modèle."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:397
msgid "Streaming Model Output"
msgstr "Sortie de modèle en streaming"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:401
msgid "The streaming model output."
msgstr "La sortie du modèle en streaming."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:407
msgid "LLM Operator"
msgstr "Opérateur LLM"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:411
msgid ""
"High-level LLM operator, supports multi-round conversation (conversation "
"window, token length and no multi-round)."
msgstr ""
"Opérateur LLM de haut niveau, prend en charge les conversations multi-tours "
"(fenêtre de conversation, longueur des tokens et sans multi-tours)."

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:441
msgid "Streaming LLM Operator"
msgstr "Opérateur LLM en streaming"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:445
msgid ""
"High-level streaming LLM operator, supports multi-round conversation "
"(conversation window, token length and no multi-round)."
msgstr ""
"Opérateur LLM de streaming de haut niveau, qui prend en charge les conversations multi-tours (fenêtre de conversation, longueur des tokens et sans multi-tours)."