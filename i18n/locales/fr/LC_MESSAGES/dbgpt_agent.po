# French translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-23 13:40+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:362
msgid "Agent Branch Operator"
msgstr "Opérateur de branchement d'Agent"

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:367
msgid ""
"Branch the workflow based on the agent actionreport nexspeakers of the "
"request."
msgstr ""
"Brancher le flux de travail en fonction du rapport d'action de l'Agent et des "
"prochains intervenants de la requête."

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:372
#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:380
msgid "Agent Request"
msgstr "Requête d'Agent"

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:375
msgid "The input value of the operator."
msgstr "La valeur d'entrée de l'opérateur."

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:383
msgid "The agent request to agent Operator."
msgstr "La requête d'Agent à l'opérateur d'Agent."

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:427
msgid "Agent Branch Join Operator"
msgstr "Opérateur de jonction de branchement d'Agent"

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:431
msgid "Just keep the first non-empty output."
msgstr "Conserver uniquement la première sortie non vide."

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:435
msgid "Agent Output"
msgstr "Sortie d'Agent"

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:438
msgid "The Agent output."
msgstr "La sortie de l'Agent."

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:443
msgid "Branch Output"
msgstr "Sortie de branche"

#:../packages/dbgpt-core/src/dbgpt/agent/core/plan/awel/agent_operator.py:446
msgid "The output value of the operator."
msgstr "La valeur de sortie de l'opérateur."