# French translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: ../packages/dbgpt-core/src/dbgpt/util/cli/flow_compat.py:32
msgid ""
"The module to scan, if not set, will scan all DB-GPT "
"modules('dbgpt,dbgpt_client,dbgpt_ext,dbgpt_serve,dbgpt_app')."
msgstr ""
"Le module à scanner, si non défini, scannera tous les modules DB-GPT "
"('dbgpt,dbgpt_client,dbgpt_ext,dbgpt_serve,dbgpt_app')."

#: ../packages/dbgpt-core/src/dbgpt/util/cli/flow_compat.py:42
msgid ""
"The output path, if not set, will print to packages/dbgpt-serve/src/"
"dbgpt_serve/flow/compat/"
msgstr ""
"Le chemin de sortie, si non défini, sera imprimé dans packages/dbgpt-serve/"
"src/dbgpt_serve/flow/compat/"

#: ../packages/dbgpt-core/src/dbgpt/util/cli/flow_compat.py:52
msgid ""
"The current version of the flow, if not set, will read from dbgpt.__version__"
msgstr ""
"La version actuelle du flux, si non définie, sera lue à partir de "
"dbgpt.__version__"

#: ../packages/dbgpt-core/src/dbgpt/util/cli/flow_compat.py:61
msgid ""
"The last version to compatible, if not set, will big than the current "
"version by one minor version."
msgstr ""
"La dernière version compatible, si non définie, sera supérieure à la version "
"actuelle d'une version mineure."

#: ../packages/dbgpt-core/src/dbgpt/util/cli/flow_compat.py:70
msgid "Update the template file."
msgstr "Mettez à jour le fichier de modèle."

#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:255
msgid "Repos"
msgstr "Dépôts"

#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:256
#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:628
#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:718
msgid "Repository"
msgstr "Dépôt"

#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:257
#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:719
msgid "Path"
msgstr "Chemin"

#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:626
msgid "dbgpts In All Repos"
msgstr "dbgpts dans tous les dépôts"

#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:629
#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:717
msgid "Type"
msgstr "Type"

#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:630
#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:716
msgid "Name"
msgstr "Nom"

#: ../packages/dbgpt-core/src/dbgpt/util/dbgpts/repo.py:714
msgid "Installed dbgpts"
msgstr "DB-GPTs installés"

#: ../packages/dbgpt-core/src/dbgpt/util/serialization/json_serialization.py:23
msgid "Json Serializer"
msgstr "Sérialiseur JSON"

#: ../packages/dbgpt-core/src/dbgpt/util/serialization/json_serialization.py:26
msgid "The serializer for serializing data with json format."
msgstr "Le sérialiseur pour sérialiser les données au format JSON."

#: ../packages/dbgpt-core/src/dbgpt/util/configure/manager.py:104
msgid ""
"Hook path, it can be a class path or a function path. eg: "
"'dbgpt.config.hooks.env_var_hook'"
msgstr "Chemin du hook, il peut s'agir d'un chemin de classe ou d'un chemin de fonction. Par exemple : 'dbgpt.config.hooks.env_var_hook'"

#: ../packages/dbgpt-core/src/dbgpt/util/configure/manager.py:113
msgid ""
"Hook init params to pass to the hook constructor(Just for class hook), must "
"be key-value pairs"
msgstr "Paramètres d'initialisation du hook à passer au constructeur du hook (uniquement pour les hooks de classe), doivent être des paires clé-valeur"

#: ../packages/dbgpt-core/src/dbgpt/util/configure/manager.py:121
msgid "Hook params to pass to the hook, must be key-value pairs"
msgstr "Paramètres du hook à passer au hook, doivent être des paires clé-valeur"

#: ../packages/dbgpt-core/src/dbgpt/util/configure/manager.py:126
msgid "Whether the hook is enabled, default is True"
msgstr "Indique si le hook est activé, la valeur par défaut est True"

#: ../packages/dbgpt-core/src/dbgpt/util/utils.py:42
msgid "Logging level, just support FATAL, ERROR, WARNING, INFO, DEBUG, NOTSET"
msgstr "Niveau de journalisation, ne prend en charge que FATAL, ERROR, WARNING, INFO, DEBUG, NOTSET"

#: ../packages/dbgpt-core/src/dbgpt/util/utils.py:58
msgid "The filename to store logs"
msgstr "Le nom du fichier pour stocker les logs"

#: ../packages/dbgpt-core/src/dbgpt/util/tracer/tracer_impl.py:251
msgid "The file to store the tracer, e.g. dbgpt_webserver_tracer.jsonl"
msgstr "Le fichier pour stocker le traceur, par exemple dbgpt_webserver_tracer.jsonl"

#: ../packages/dbgpt-core/src/dbgpt/util/tracer/tracer_impl.py:258
msgid "The root operation name of the tracer"
msgstr "Le nom de l'opération racine du traceur"

#: ../packages/dbgpt-core/src/dbgpt/util/tracer/tracer_impl.py:264
msgid "The exporter of the tracer, e.g. telemetry"
msgstr "L'exportateur du traceur, par exemple la télémesure"

#: ../packages/dbgpt-core/src/dbgpt/util/tracer/tracer_impl.py:271
msgid ""
"The endpoint of the OpenTelemetry Protocol, you can set '$"
"{env:OTEL_EXPORTER_OTLP_TRACES_ENDPOINT}' to use the environment variable"
msgstr ""
"Le point de terminaison du protocole OpenTelemetry. Vous pouvez définir '${env:OTEL_EXPORTER_OTLP_TRACES_ENDPOINT}' pour utiliser la variable d'environnement."

#: ../packages/dbgpt-core/src/dbgpt/util/tracer/tracer_impl.py:281
msgid ""
"Whether to use insecure connection, you can set '$"
"{env:OTEL_EXPORTER_OTLP_TRACES_INSECURE}' to use the environment "
msgstr ""
"Indique si une connexion non sécurisée doit être utilisée. Vous pouvez définir '${env:OTEL_EXPORTER_OTLP_TRACES_INSECURE}' pour utiliser la variable d'environnement."

#: ../packages/dbgpt-core/src/dbgpt/util/tracer/tracer_impl.py:290
msgid ""
"The timeout of the connection, in seconds, you can set '$"
"{env:OTEL_EXPORTER_OTLP_TRACES_TIMEOUT}' to use the environment "
msgstr ""
"Le délai d'attente de la connexion, en secondes. Vous pouvez définir '$"
"{env:OTEL_EXPORTER_OTLP_TRACES_TIMEOUT}' pour utiliser la variable "
"d'environnement."

#: ../packages/dbgpt-core/src/dbgpt/util/tracer/tracer_impl.py:298
msgid "The class of the tracer storage"
msgstr "La classe de stockage du traceur"