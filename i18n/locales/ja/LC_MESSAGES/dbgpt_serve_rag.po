# Japanese translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:44
msgid "Knowledge Space Operator"
msgstr "知識空間オペレーター"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:47
msgid "knowledge space retriever operator."
msgstr "知識空間検索オペレーター。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:48
msgid "Query"
msgstr "クエリ"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:48
msgid "user query"
msgstr "ユーザークエリ"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:51
#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:54
msgid "related chunk content"
msgstr "関連するチャンク内容"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:59
msgid "Space Name"
msgstr "空間名"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:65
msgid "space name."
msgstr "空間名。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:125
msgid "Knowledge Space Prompt Builder Operator"
msgstr "知識空間プロンプトビルダーオペレーター"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:127
msgid "Build messages from prompt template and chat history."
msgstr "プロンプトテンプレートとチャット履歴からメッセージを構築します。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:132
msgid "Chat Prompt Template"
msgstr "チャットプロンプトテンプレート"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:135
msgid "The chat prompt template."
msgstr "チャットプロンプトテンプレート。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:138
msgid "History Key"
msgstr "履歴キー"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:143
msgid "The key of history in prompt dict."
msgstr "プロンプト辞書内の履歴のキー。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:146
msgid "String History"
msgstr "文字列履歴"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:151
msgid "Whether to convert the history to string."
msgstr "履歴を文字列に変換するかどうか。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:156
#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:160
msgid "user input"
msgstr "ユーザー入力"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:163
msgid "space related context"
msgstr "空間関連のコンテキスト"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:167
msgid "context of knowledge space."
msgstr "知識空間のコンテキスト。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:170
msgid "History"
msgstr "履歴"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:174
msgid "The history."
msgstr "履歴。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:179
msgid "Formatted Messages"
msgstr "整形済みメッセージ"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/operators/knowledge_space.py:183
msgid "The formatted messages."
msgstr "整形済みメッセージ。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:20
msgid "RAG Serve Configurations"
msgstr "RAG サーブ設定"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:23
msgid "This configuration is for the RAG serve module."
msgstr "この設定はRAGサーブモジュール用です。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:34
#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:38
msgid "Embedding Model"
msgstr "埋め込みモデル"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:42
msgid "Whether to verify the SSL certificate of the database"
msgstr "データベースのSSL証明書を検証するかどうか"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:48
msgid ""
"The default thread pool size, If None, use default config of python thread "
"pool"
msgstr ""
"デフォルトのスレッドプールサイズ。Noneの場合、Pythonスレッドプールのデフォルト設定を使用します。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:55
msgid "knowledge search top k"
msgstr "知識検索の上位k件"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:59
msgid "knowledge search top similarity score"
msgstr "知識検索の上位類似度スコア"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:63
msgid "knowledge search rewrite"
msgstr "知識検索の再構築"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:67
msgid "knowledge max chunks once load"
msgstr "知識の一度に読み込む最大チャンク数"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:71
msgid "knowledge max load thread"
msgstr "知識の最大読み込みスレッド数"

#: ../packages/dbgpt-serve/src/dbgpt_serve/rag/config.py:75
msgid "knowledge rerank top k"
msgstr "知識の再ランキング上位 k 件"