# Japanese translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:32
msgid "All AWEL Flows"
msgstr "すべての AWEL フロー"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:33
msgid "Fetch all AWEL flows in the system"
msgstr "システム内のすべての AWEL フローを取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:40
msgid "All AWEL Flow Nodes"
msgstr "すべての AWEL フローノード"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:41
msgid "Fetch all AWEL flow nodes in the system"
msgstr "システム内のすべての AWEL フローノードを取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:48
msgid "All Variables"
msgstr "すべての変数"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:49
msgid "Fetch all variables in the system"
msgstr "システム内のすべての変数を取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:56
msgid "All Secrets"
msgstr "すべてのシークレット"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:57
msgid "Fetch all secrets in the system"
msgstr "システム内のすべてのシークレットを取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:64
msgid "All LLMs"
msgstr "すべての Large Language Model"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:65
msgid "Fetch all LLMs in the system"
msgstr "システム内のすべての Large Language Model を取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:72
msgid "All Embeddings"
msgstr "すべての埋め込みモデル"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:73
msgid "Fetch all embeddings models in the system"
msgstr "システム内のすべての埋め込みモデルを取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:80
msgid "All Rerankers"
msgstr "すべての再ランキングモデル"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:81
msgid "Fetch all rerankers in the system"
msgstr "システム内のすべての再ランキングモデルを取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:88
msgid "All Data Sources"
msgstr "すべてのデータソース"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:89
msgid "Fetch all data sources in the system"
msgstr "システム内のすべてのデータソースを取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:96
msgid "All Agents"
msgstr "すべてのエージェント"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:97
msgid "Fetch all agents in the system"
msgstr "システム内のすべてのエージェントを取得します"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:104
msgid "All Knowledge Spaces"
msgstr "すべての知識空間"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/service/variables_service.py:105
msgid "Fetch all knowledge spaces in the system"
msgstr "システム内のすべての知識空間を取得する"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/config.py:24
msgid "AWEL Flow Serve Configurations"
msgstr "AWEL フローサービス設定"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/config.py:27
msgid "This configuration is for the flow serve module."
msgstr "この設定はフローサービスモジュール用です。"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/config.py:38
msgid "Interval to load dbgpts from installed packages"
msgstr "インストール済みパッケージから DB-GPT を読み込む間隔"

#: ../packages/dbgpt-serve/src/dbgpt_serve/flow/config.py:41
msgid "The key to encrypt the data"
msgstr "データを暗号化するためのキー"